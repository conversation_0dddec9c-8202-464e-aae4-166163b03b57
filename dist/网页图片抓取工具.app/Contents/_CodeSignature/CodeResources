<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/PySide6/Qt/translations/qt_ar.qm</key>
		<data>
		KRRGiDA52ynAP9C/R1Ph6oOCiLA=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_bg.qm</key>
		<data>
		pL4wUSZh7QzIAlbmOsxZyJWmANk=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_ca.qm</key>
		<data>
		hhagY04qgpLvYn87GwVCGElxNrI=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_cs.qm</key>
		<data>
		gX1GFeTg/EAGQvGwRpLWEDn1XHg=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_da.qm</key>
		<data>
		qB/6hrfabhdI1dTdXy+8gTtyw4o=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_de.qm</key>
		<data>
		5sjoR3izOmpbnmXyBCtMeN5HbqA=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_en.qm</key>
		<data>
		SIT9mvaJBke3rxrvpX84zKSa2Jk=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_es.qm</key>
		<data>
		jUxtNjW95x/bBtjz6mlWkEid8Ws=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_fa.qm</key>
		<data>
		UeSt6KvJ6HgHvki6qANY+NpaC7s=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_fi.qm</key>
		<data>
		ufvhtLrOvP/SjfxIN6Wy72EhWO8=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_fr.qm</key>
		<data>
		P8xmqZT4uiJFvkGTph86WQ+nLeQ=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_gd.qm</key>
		<data>
		JRI2FUgpR3chduBV5KdAQ7L7yqA=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_gl.qm</key>
		<data>
		615yBTVc/GvLTfJ+IkB5hCyXspY=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_he.qm</key>
		<data>
		+1LsLXAn6rV6GMcjsEjC+tkgkPE=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_ar.qm</key>
		<data>
		V1G+loF7tq58nanx+6f0LzHPzF0=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_bg.qm</key>
		<data>
		cla+OQuIoFPAJSSIxEO+Qvby2So=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_ca.qm</key>
		<data>
		Xi/rn5Hz1xYk0QW/3MzP9jYHVt4=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_cs.qm</key>
		<data>
		fwYc8MaZ0SWlUx40gMIZZEUvReo=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_da.qm</key>
		<data>
		x38KFIJiqRZ58ZaJ5HkLdU1F1dU=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_de.qm</key>
		<data>
		cIhut+9WayltCBS9TCRArBdmmdY=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_en.qm</key>
		<data>
		SIT9mvaJBke3rxrvpX84zKSa2Jk=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_es.qm</key>
		<data>
		qRiuFaDfGHx3ib6FmagOJ58DmWQ=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_fr.qm</key>
		<data>
		48mjB+pHn71bua/OemrK0QnRAls=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_gl.qm</key>
		<data>
		WJzgmfLB2SWBtc8OF75Jor8AFNQ=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_hr.qm</key>
		<data>
		9KxH3kxpESyJlULlvLBWxN6qS1g=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_hu.qm</key>
		<data>
		1vB59A+7gTsCk8HSIQuucIQJL+w=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_it.qm</key>
		<data>
		ZZNaZ8c/Gfz2Aj+5UDClrK+dohw=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_ja.qm</key>
		<data>
		m3N+0F+A/l082PWIzOwWuxHdNWA=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_ko.qm</key>
		<data>
		rqIuSg0hOWczgCx6tzjd0Dc3t9Y=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_nl.qm</key>
		<data>
		XBilR68q3A4KAhAnMG4X5XekBDY=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_nn.qm</key>
		<data>
		NQMMNLoLNSxhsFkszNhWkRXX7rw=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_pl.qm</key>
		<data>
		igm9OJmFQ7dOJnNHjt1U+0u90Gg=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_pt_BR.qm</key>
		<data>
		G1yOhdQK6XUsQosgN/Z+XsDbrL0=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_ru.qm</key>
		<data>
		Ogw2mHKxEqFXKqF+64FLFosiXZg=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_sk.qm</key>
		<data>
		4+X0WCzF+v5t9DZE0RSEhhAjwIQ=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_sl.qm</key>
		<data>
		yU6DJZXsdlNwVTRo+HwC2359E4o=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_tr.qm</key>
		<data>
		WC7gedvUIADDeOBwHSZAV1BSTbo=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_uk.qm</key>
		<data>
		XfOyE9mFo7vbR2s3t3gNfX3xfkE=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_zh_CN.qm</key>
		<data>
		TtQ/AmgRy0aMnHkOQJjK2FQ1/t0=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_help_zh_TW.qm</key>
		<data>
		ZMyrJ0P8wW5D8Le2g7ws2XbKbtg=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_hr.qm</key>
		<data>
		jM5pEqiQWu8a/L1rlRue+istxoM=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_hu.qm</key>
		<data>
		uGeHNYo3K9DSj/09YSYXLvcsp+Q=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_it.qm</key>
		<data>
		HxX7SC2HFKVO8x6hPBN4xcyBn+s=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_ja.qm</key>
		<data>
		AKGJqOdNjXpTLGNG6Zd0rQNElBA=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_ko.qm</key>
		<data>
		AnsGXCOBcw2OR4DnRey9IdsZNPo=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_lt.qm</key>
		<data>
		4idmpJYS95FWxVDYPGwjA0XdpDM=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_lv.qm</key>
		<data>
		s/U/wIcFOtrd7RFIqEElnaXxjvA=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_nl.qm</key>
		<data>
		TbrDNGM1xnD1tLufYerFUkf9yFY=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_nn.qm</key>
		<data>
		5FWrnrzNvy59ItO31jY0dL4uz24=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_pl.qm</key>
		<data>
		LHIwfv5qOzNptwC7H1IyAtzA3Tg=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_pt_BR.qm</key>
		<data>
		h1JF5Na/E0R2PaT826u5lF5jms8=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_pt_PT.qm</key>
		<data>
		zBErnJUTvPdJfzQXFotMip92QKk=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_ru.qm</key>
		<data>
		lEygrV9Dssn4qHgu7/HCxUaf2dg=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_sk.qm</key>
		<data>
		BnnDLjLb71JsO4c7Y8aGLN3HdZ0=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_sl.qm</key>
		<data>
		nzyFwRWig+UjDR7q2EyMtzpx+gM=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_sv.qm</key>
		<data>
		rlk0jgwuT4b5nabPXas7fpJQS3w=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_tr.qm</key>
		<data>
		GNOMeE5IepUdyPQyUALgG0ok3RY=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_uk.qm</key>
		<data>
		m+fwODheG3jzS2si4ZrnrROCKmg=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_zh_CN.qm</key>
		<data>
		9ETcBWcd3c6+IpD3l2/YMWWCxBs=
		</data>
		<key>Resources/PySide6/Qt/translations/qt_zh_TW.qm</key>
		<data>
		2dNImYY974KBLyqiBisBNKZmeqA=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_ar.qm</key>
		<data>
		If0TGyO90bunu7hvPtXIOHb0Vjg=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_bg.qm</key>
		<data>
		ZUQJzfP1UVVZV9Pbz41qDY8DpsU=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_ca.qm</key>
		<data>
		bnNXbuU0mHDutyNzWW2taXVoh1U=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_cs.qm</key>
		<data>
		Og53dTnFG7Ze52uOHY3OQ4bLyIY=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_da.qm</key>
		<data>
		cLGbKmkU2n1in1d/iYdVNxPNXT8=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_de.qm</key>
		<data>
		D80BPLDIOMo8PAmlSZ/e3ge1/II=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_en.qm</key>
		<data>
		SIT9mvaJBke3rxrvpX84zKSa2Jk=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_es.qm</key>
		<data>
		gJ5YDNvy/9oQx3+L6brAgZeMECs=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_fa.qm</key>
		<data>
		sK2+lQeQkkJCgG9nFxLFe1hLWPs=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_fi.qm</key>
		<data>
		e1MTzaEmu3hjABSZ+2b7G1bCVfw=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_fr.qm</key>
		<data>
		Fwg5qTenfFiGFP3zEGPBZd3Ba4Y=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_gd.qm</key>
		<data>
		JPf/gJ4vEcV5zTiP6lpMVS/41NA=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_he.qm</key>
		<data>
		2znGuqRDqpuyCAQ+9/t+NAPBLZA=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_hr.qm</key>
		<data>
		yVpBY4hSHvW9s+5dEenc1M4i670=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_hu.qm</key>
		<data>
		u/NcBBd88pC0P30lM75EoV2SnQI=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_it.qm</key>
		<data>
		mK16iJ2yBZ0webAzX5vxjegfA14=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_ja.qm</key>
		<data>
		NYSDN7XwpLM7o5KaTZPdCaL6QXA=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_ko.qm</key>
		<data>
		8Q6IJ2Lc0uYAQb3WzFdZj8PfQ0M=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_lv.qm</key>
		<data>
		lTjE2LualcDZ3FfHcIqZ3VOjLR8=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_nl.qm</key>
		<data>
		XM6JLNftkkOwtXkj7pLHmtYmFTs=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_nn.qm</key>
		<data>
		afxH/zozrcI0FfT4cnIFOZjWFJo=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_pl.qm</key>
		<data>
		drlyJaEd0fd8rG7xRIEvkb2HNL0=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_pt_BR.qm</key>
		<data>
		/RNzQXx+k9Y8l7O+eVbNfdgM6+k=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_ru.qm</key>
		<data>
		QF9FNhpTfHkjwkDVGw/xxGYhwgM=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_sk.qm</key>
		<data>
		AhG0kRtbdMwaRsD8qH079WMqpEo=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_tr.qm</key>
		<data>
		Th5ro3ckKCJ8sDN0cAa0iH5dmtE=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_uk.qm</key>
		<data>
		Y6FDJ9DPCUHW1rWL+n6LEDN/VXs=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_zh_CN.qm</key>
		<data>
		NWH5X2RrIXLS6HCXO22B6YPtAKY=
		</data>
		<key>Resources/PySide6/Qt/translations/qtbase_zh_TW.qm</key>
		<data>
		YveJ6PoME8X9T+JQV2v0WwfIV4E=
		</data>
		<key>Resources/base_library.zip</key>
		<data>
		MVRJbVawlJyOgF5dONCytlx10Ow=
		</data>
		<key>Resources/certifi/cacert.pem</key>
		<data>
		fylep0/H7Qrw6SvggHH7C3bIUJ4=
		</data>
		<key>Resources/certifi/py.typed</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/icon.icns</key>
		<data>
		y0ogExPIMaAOfSJrRsFAMUKcC6k=
		</data>
		<key>Resources/image_downloader/__init__.py</key>
		<data>
		PPqHNNGKc0x35tDzuhCZTVMC+FI=
		</data>
		<key>Resources/image_downloader/__pycache__/__init__.cpython-311.pyc</key>
		<data>
		gNJsLY1tueHZTeiuiGT1yXpoVZk=
		</data>
		<key>Resources/image_downloader/__pycache__/downloader.cpython-311.pyc</key>
		<data>
		MPKHVxVIld2IX6H5JWbNc4lukb0=
		</data>
		<key>Resources/image_downloader/__pycache__/image_processor.cpython-311.pyc</key>
		<data>
		S1OueEMoT00ubIanOgpmx2teNhE=
		</data>
		<key>Resources/image_downloader/__pycache__/logger.cpython-311.pyc</key>
		<data>
		fijSruEeEQDqUdGV46fDFdKW/m4=
		</data>
		<key>Resources/image_downloader/__pycache__/utils.cpython-311.pyc</key>
		<data>
		uVw25UJdEqV0M3TLClwB3egMNzs=
		</data>
		<key>Resources/image_downloader/downloader.py</key>
		<data>
		a3UPVupChH4IrBlVjRYm1RlxHVQ=
		</data>
		<key>Resources/image_downloader/image_processor.py</key>
		<data>
		G7zOvfCxJyMdNFAn1fjVQfKUXyA=
		</data>
		<key>Resources/image_downloader/logger.py</key>
		<data>
		IhYcQ0QHDPUkkJ+4qK9ZGAAIq6w=
		</data>
		<key>Resources/image_downloader/utils.py</key>
		<data>
		P3RVLGve/4Mj88IsBlw3ZAlKP2c=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/LICENSE</key>
		<data>
		K4uBUimqimHkg/tLoFiLi2xJGJA=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/METADATA</key>
		<data>
		nPSbev91rJ4PppQPWjQgj6hwMMo=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/RECORD</key>
		<data>
		fM5RKCqPZNSNeS1YVlmIXBdA9gA=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/WHEEL</key>
		<data>
		pByoQsqfpfXS76mODiPJv8ub9e4=
		</data>
		<key>Resources/importlib_metadata-8.6.1.dist-info/top_level.txt</key>
		<data>
		TmH5Jk3nR4O1kkJJvP4bBvF4ua0=
		</data>
		<key>Resources/lxml/isoschematron/resources/rng/iso-schematron.rng</key>
		<data>
		Go1op5prM1pmBGPMmbTo7uFqB6E=
		</data>
		<key>Resources/lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl</key>
		<data>
		gw8JUVGbfoiM4PnGl4RuYCHn/sA=
		</data>
		<key>Resources/lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl</key>
		<data>
		+27UKlCTWg5312vRXldA7ijU6zI=
		</data>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl</key>
		<data>
		47+62eTQoIRbDwHznw46HeAk23A=
		</data>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl</key>
		<data>
		OGl2iT6BakNol6cYdYs6u/JoaeE=
		</data>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl</key>
		<data>
		JnOKlPhxa+9BjjlodWBP5w4YSI0=
		</data>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl</key>
		<data>
		QekDoEjBN2P/f6ofaCbqkTKe9ms=
		</data>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl</key>
		<data>
		0b3cqpseVdF8eLWXy1wcOB5WjeY=
		</data>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt</key>
		<data>
		70CMM3nNaMhY+q1Fr/J9H9teBhk=
		</data>
		<key>Resources/wheel-0.40.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/wheel-0.40.0.dist-info/LICENSE.txt</key>
		<data>
		WznEWpGlVuXxWZYE8XmeQCf6DmA=
		</data>
		<key>Resources/wheel-0.40.0.dist-info/METADATA</key>
		<data>
		kxMm5yNqeU55hQ+qqb6gIHBHELM=
		</data>
		<key>Resources/wheel-0.40.0.dist-info/RECORD</key>
		<data>
		NG9flQ2Pp0umk+YK06/tOpA9F9U=
		</data>
		<key>Resources/wheel-0.40.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/wheel-0.40.0.dist-info/WHEEL</key>
		<data>
		WXXrYEOGOw0Bil11EpPzjguOKHQ=
		</data>
		<key>Resources/wheel-0.40.0.dist-info/direct_url.json</key>
		<data>
		JMoYMdGERFuoHU3c+tfB5QpB+XE=
		</data>
		<key>Resources/wheel-0.40.0.dist-info/entry_points.txt</key>
		<data>
		46EsQhVip32QoT2FOaOg9NMig1k=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/PIL/.dylibs</key>
		<dict>
			<key>symlink</key>
			<string>__dot__dylibs</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libXau.6.0.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			sGJ0Pi1DHwsWUFFG6/091pJKO+Y=
			</data>
			<key>requirement</key>
			<string>cdhash H"0903b48f967b1ee0accbf3fead5786e2c3566503" or cdhash H"b062743e2d431f0b16505146ebfd3dd6924a3be6"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblcms2.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			GK4xQaSxhFDacsQ1IOBjk+WrJMo=
			</data>
			<key>requirement</key>
			<string>cdhash H"56b8520e9115031eb5646b564fbab5b3db123690" or cdhash H"18ae3141a4b18450da72c43520e06393e5ab24ca"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			dkFHIDE+jSiJaRaPFk6WYqbeGEY=
			</data>
			<key>requirement</key>
			<string>cdhash H"ddedc032a38729502dbb4c8341dc9aa1ee321787" or cdhash H"76414720313e8d288969168f164e9662a6de1846"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libopenjp2.2.5.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			rpOFUCBj0j+z02anBkETjqsMIf4=
			</data>
			<key>requirement</key>
			<string>cdhash H"c87e75b9ff9d676faee54826ffa213193e7f351b" or cdhash H"ae9385502063d23fb3d366a70641138eab0c21fe"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libsharpyuv.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			0dO6gdFXzSB/GLnj7UQ78nsYGgk=
			</data>
			<key>requirement</key>
			<string>cdhash H"bab5219e46a5dc081198890f7761ca49945d3755" or cdhash H"d1d3ba81d157cd207f18b9e3ed443bf27b181a09"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libtiff.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			7l7xTMBdxc+7HF4Bc2JLLWkLv1w=
			</data>
			<key>requirement</key>
			<string>cdhash H"4f3d09c00aa2c2cc228bce93a396fb8504f66eb3" or cdhash H"ee5ef14cc05dc5cfbb1c5e0173624b2d690bbf5c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebp.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			lxGja+bfcdDTKTEvDry4gZr651c=
			</data>
			<key>requirement</key>
			<string>cdhash H"0889601548652585e630e9bfa76a8ad7d31ea722" or cdhash H"9711a36be6df71d0d329312f0ebcb8819afae757"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpdemux.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			bz8ww8530mO1iJKiC/tFbvFBZ5M=
			</data>
			<key>requirement</key>
			<string>cdhash H"0771c7790f6f86dbf2b26aadcaf9282545d1678f" or cdhash H"6f3f30c3ce77d263b58892a20bfb456ef1416793"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpmux.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			I8HTmToRcZUtpS05hPZG9LttMog=
			</data>
			<key>requirement</key>
			<string>cdhash H"e15d364b8c71cbff17965e01a5d95035d25e6261" or cdhash H"23c1d3993a1171952da52d3984f646f4bb6d3288"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libxcb.1.1.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			6kDYM1A9srdfR2ZppeJucnsv4+A=
			</data>
			<key>requirement</key>
			<string>cdhash H"8da99cfded1436ab59a62ad5791659ece58bc571" or cdhash H"ea40d833503db2b75f476669a5e26e727b2fe3e0"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libz.1.2.13.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			de6tAHH0erMO+nPsQE06WCm1bjo=
			</data>
			<key>requirement</key>
			<string>cdhash H"d96016e94c50b3a1e576743310dcc89c7677e06b" or cdhash H"75eead0071f47ab30efa73ec404d3a5829b56e3a"</string>
		</dict>
		<key>Frameworks/PIL/_imaging.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			C2rIewjtSrmSKKMypitxCWY0sHE=
			</data>
			<key>requirement</key>
			<string>cdhash H"6db0e48800f6d9575d642769cdb6c2b5067b072d" or cdhash H"0b6ac87b08ed4ab99228a332a62b71096634b071"</string>
		</dict>
		<key>Frameworks/PIL/_imagingcms.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LokdkEaDuO7t9zt9smX6GL2c0pU=
			</data>
			<key>requirement</key>
			<string>cdhash H"d2fb38bcb996938168581ce293fc21486d644af8" or cdhash H"2e891d904683b8eeedf73b7db265fa18bd9cd295"</string>
		</dict>
		<key>Frameworks/PIL/_imagingtk.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ppsObHlHg+cLv7iR9spYEd1ITPY=
			</data>
			<key>requirement</key>
			<string>cdhash H"915f1464107e6d8e3f945f0ef353f1f2b6404965" or cdhash H"a69b0e6c794783e70bbfb891f6ca5811dd484cf6"</string>
		</dict>
		<key>Frameworks/PIL/_webp.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			XUcyDqwIKy/6iSKF33icXxtRU2c=
			</data>
			<key>requirement</key>
			<string>cdhash H"a959caee616b223e50054d027cffe0f208fc8f23" or cdhash H"5d47320eac082b2ffa892285df789c5f1b515367"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtCore.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			j1Cyib4GaQVQq8oI0sAyWVCRkLs=
			</data>
			<key>requirement</key>
			<string>cdhash H"8f50b289be06690550abca08d2c03259509190bb"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtDBus.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			HQhPEjDGi0F2XRS89IwTQUnFz/s=
			</data>
			<key>requirement</key>
			<string>cdhash H"1d084f1230c68b41765d14bcf48c134149c5cffb"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtGui.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			EA72UMU0AU6yUDic41pMDS2LRI8=
			</data>
			<key>requirement</key>
			<string>cdhash H"100ef650c534014eb250389ce35a4c0d2d8b448f"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtNetwork.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			X9Zkn/m4fd9Z2BWMQcTsV/ZXQkE=
			</data>
			<key>requirement</key>
			<string>cdhash H"5fd6649ff9b87ddf59d8158c41c4ec57f6574241"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtOpenGL.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			v7u/SsXMTjn2wzG0SoMo+RPp3Kw=
			</data>
			<key>requirement</key>
			<string>cdhash H"bfbbbf4ac5cc4e39f6c331b44a8328f913e9dcac"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtPdf.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			VmuQx9FjxmKRAU7EYJW1WrpkqYI=
			</data>
			<key>requirement</key>
			<string>cdhash H"566b90c7d163c66291014ec46095b55aba64a982"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtQml.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			bII5qMk+/SlAiqVq2ijaEN8c6vg=
			</data>
			<key>requirement</key>
			<string>cdhash H"6c8239a8c93efd29408aa56ada28da10df1ceaf8"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtQmlModels.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			/2uDg3zDkzJZsIWN4s+5b2LLr3E=
			</data>
			<key>requirement</key>
			<string>cdhash H"ff6b83837cc3933259b0858de2cfb96f62cbaf71"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtQuick.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			6bxeHr/PDn85uSHRFfVvX6NySNA=
			</data>
			<key>requirement</key>
			<string>cdhash H"e9bc5e1ebfcf0e7f39b921d115f56f5fa37248d0"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtSvg.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			AZpkql1XEw3K+xj49FPG6cl67cI=
			</data>
			<key>requirement</key>
			<string>cdhash H"019a64aa5d57130dcafb18f8f453c6e9c97aedc2"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtVirtualKeyboard.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			bouZuLUlIgAFzktjQVUeN+ij+E0=
			</data>
			<key>requirement</key>
			<string>cdhash H"6e8b99b8b525220005ce4b6341551e37e8a3f84d"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/lib/QtWidgets.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			NzpMknOZE+aYbtVR3194+b6yl4o=
			</data>
			<key>requirement</key>
			<string>cdhash H"373a4c92739913e6986ed551df5f78f9beb2978a"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/generic/libqtuiotouchplugin.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			thZsyw1L+KggZc/v/7beV5dmE+0=
			</data>
			<key>requirement</key>
			<string>cdhash H"b6166ccb0d4bf8a82065cfefffb6de57976613ed"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/iconengines/libqsvgicon.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			NeBGH/XOcz08iFSs3YogaP4Fang=
			</data>
			<key>requirement</key>
			<string>cdhash H"35e0461ff5ce733d3c8854acdd8a2068fe056a78"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqgif.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			on0QWz5Ui19+bCmcloJLMmW8Qxc=
			</data>
			<key>requirement</key>
			<string>cdhash H"a27d105b3e548b5f7e6c299c96824b3265bc4317"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqicns.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			6v7UOhmaPEyStHJUs3nxX/yiE6c=
			</data>
			<key>requirement</key>
			<string>cdhash H"eafed43a199a3c4c92b47254b379f15ffca213a7"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqico.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			eTuOpB6pAT61xH1IOOUPEQV2FQ4=
			</data>
			<key>requirement</key>
			<string>cdhash H"793b8ea41ea9013eb5c47d4838e50f110576150e"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqjpeg.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			kh4NZt59D2avHnJKt95x6S2AlyQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"921e0d66de7d0f66af1e724ab7de71e92d809724"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqmacheif.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			K0uCYc8ZAMbv4xco34SrUvkwkd4=
			</data>
			<key>requirement</key>
			<string>cdhash H"2b4b8261cf1900c6efe31728df84ab52f93091de"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqmacjp2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			fbXvOqMA6n8QZNylV976PHtuUGQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"7db5ef3aa300ea7f1064dca557defa3c7b6e5064"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqpdf.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			lO5v3i0K4gpbbnoIkK+myuNTTf0=
			</data>
			<key>requirement</key>
			<string>cdhash H"94ee6fde2d0ae20a5b6e7a0890afa6cae3534dfd"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqsvg.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			4E6+yPhBZVW1xjTb9a9DJl5MWMI=
			</data>
			<key>requirement</key>
			<string>cdhash H"e04ebec8f8416555b5c634dbf5af43265e4c58c2"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqtga.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			16IKMkmbNrGoBT8rqiYcaS24Ato=
			</data>
			<key>requirement</key>
			<string>cdhash H"d7a20a32499b36b1a8053f2baa261c692db802da"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqtiff.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			XvyrhUGgPeZS8bUcxXE5B3d/YX0=
			</data>
			<key>requirement</key>
			<string>cdhash H"5efcab8541a03de652f1b51cc5713907777f617d"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqwbmp.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			54QBE9M1CQLKzDkGC80HfFa/J6c=
			</data>
			<key>requirement</key>
			<string>cdhash H"e7840113d3350902cacc39060bcd077c56bf27a7"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/imageformats/libqwebp.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			xcvQoFzSXmZJQe8xnVJzmmGcjh0=
			</data>
			<key>requirement</key>
			<string>cdhash H"c5cbd0a05cd25e664941ef319d52739a619c8e1d"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/networkinformation/libqscnetworkreachability.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			jvsN7zeXk+NUiXXk1sf9QCf9Vas=
			</data>
			<key>requirement</key>
			<string>cdhash H"8efb0def379793e3548975e4d6c7fd4027fd55ab"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/platforminputcontexts/libqtvirtualkeyboardplugin.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			G5bozbrlv407PxyEQTkCSTSaGd0=
			</data>
			<key>requirement</key>
			<string>cdhash H"1b96e8cdbae5bf8d3b3f1c8441390249349a19dd"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/platforms/libqcocoa.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			MMnyAow+gui8hlZTKzqSu6+p608=
			</data>
			<key>requirement</key>
			<string>cdhash H"30c9f2028c3e82e8bc8656532b3a92bbafa9eb4f"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/platforms/libqminimal.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			pfKv4mQJ40R1f43JzNZ08pbkMxo=
			</data>
			<key>requirement</key>
			<string>cdhash H"a5f2afe26409e344757f8dc9ccd674f296e4331a"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/platforms/libqoffscreen.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ON9Iw47smDxeSNkbHHyvdmSrNnM=
			</data>
			<key>requirement</key>
			<string>cdhash H"38df48c38eec983c5e48d91b1c7caf7664ab3673"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/styles/libqmacstyle.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			+VPVtLzsNYtZ2YtWP2JEC4Dh/Bw=
			</data>
			<key>requirement</key>
			<string>cdhash H"f953d5b4bcec358b59d98b563f62440b80e1fc1c"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/tls/libqcertonlybackend.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Dg/SNU6tedukS25C6AtniiqAdVc=
			</data>
			<key>requirement</key>
			<string>cdhash H"0e0fd2354ead79dba44b6e42e80b678a2a807557"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/tls/libqopensslbackend.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			UfBptzuObAwrAy0uzU87pWa/2WI=
			</data>
			<key>requirement</key>
			<string>cdhash H"51f069b73b8e6c0c2b032d2ecd4f3ba566bfd962"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/plugins/tls/libqsecuretransportbackend.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			FiUf9ku9VdREkuYEu9Fzpf7rvAE=
			</data>
			<key>requirement</key>
			<string>cdhash H"16251ff64bbd55d44492e604bbd173a5feebbc01"</string>
		</dict>
		<key>Frameworks/PySide6/Qt/translations</key>
		<dict>
			<key>symlink</key>
			<string>../../../Resources/PySide6/Qt/translations</string>
		</dict>
		<key>Frameworks/PySide6/QtCore.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oO70gbK9L4EYp0il9eNqQ+iyT4A=
			</data>
			<key>requirement</key>
			<string>cdhash H"a0eef481b2bd2f8118a748a5f5e36a43e8b24f80"</string>
		</dict>
		<key>Frameworks/PySide6/QtDBus.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			sa4uqTTioLJHis4YLj5cFrEYIQQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"b1ae2ea934e2a0b2478ace182e3e5c16b1182104"</string>
		</dict>
		<key>Frameworks/PySide6/QtGui.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kN/0dHl0g69E0W06jBRx/UKYDjM=
			</data>
			<key>requirement</key>
			<string>cdhash H"90dff474797483af44d16d3a8c1471fd42980e33"</string>
		</dict>
		<key>Frameworks/PySide6/QtNetwork.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			mBLohfiY0vR+Vo5J7NETetsuQyk=
			</data>
			<key>requirement</key>
			<string>cdhash H"9812e885f898d2f47e568e49ecd1137adb2e4329"</string>
		</dict>
		<key>Frameworks/PySide6/QtWidgets.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			SsgRIs46xzwanX0bIScmpoYDZf0=
			</data>
			<key>requirement</key>
			<string>cdhash H"4ac81122ce3ac73c1a9d7d1b212726a6860365fd"</string>
		</dict>
		<key>Frameworks/PySide6/libpyside6.abi3.6.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			t2KRKm3ImxUgf877PE/GveIeA4Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"b762912a6dc89b15207fcefb3c4fc6bde21e0384"</string>
		</dict>
		<key>Frameworks/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.11/Python</string>
		</dict>
		<key>Frameworks/Python.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			Pw+bqlemyzIdpU47Xpm1Txvn5OI=
			</data>
			<key>requirement</key>
			<string>cdhash H"3f0f9baa57a6cb321da54e3b5e99b54f1be7e4e2"</string>
		</dict>
		<key>Frameworks/QtCore</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtCore.framework/Versions/A/QtCore</string>
		</dict>
		<key>Frameworks/QtDBus</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtDBus.framework/Versions/A/QtDBus</string>
		</dict>
		<key>Frameworks/QtGui</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtGui.framework/Versions/A/QtGui</string>
		</dict>
		<key>Frameworks/QtNetwork</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtNetwork.framework/Versions/A/QtNetwork</string>
		</dict>
		<key>Frameworks/QtOpenGL</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtOpenGL.framework/Versions/A/QtOpenGL</string>
		</dict>
		<key>Frameworks/QtPdf</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtPdf.framework/Versions/A/QtPdf</string>
		</dict>
		<key>Frameworks/QtQml</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtQml.framework/Versions/A/QtQml</string>
		</dict>
		<key>Frameworks/QtQmlModels</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtQmlModels.framework/Versions/A/QtQmlModels</string>
		</dict>
		<key>Frameworks/QtQuick</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtQuick.framework/Versions/A/QtQuick</string>
		</dict>
		<key>Frameworks/QtSvg</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtSvg.framework/Versions/A/QtSvg</string>
		</dict>
		<key>Frameworks/QtVirtualKeyboard</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/QtVirtualKeyboard</string>
		</dict>
		<key>Frameworks/QtWidgets</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtWidgets.framework/Versions/A/QtWidgets</string>
		</dict>
		<key>Frameworks/_brotli.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			My6C+iC7DaLNf1SdASE6D5CaMEs=
			</data>
			<key>requirement</key>
			<string>cdhash H"5ded3e5b6ef7e43cb0858c8ce1443dc7e39dee52" or cdhash H"332e82fa20bb0da2cd7f549d01213a0f909a304b"</string>
		</dict>
		<key>Frameworks/_cffi_backend.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hd0fVLOtoXUsktajAsq8fTBYcg8=
			</data>
			<key>requirement</key>
			<string>cdhash H"7adaafefac455022d5d50fd79f2d5489d33993c6" or cdhash H"85dd1f54b3ada1752c92d6a302cabc7d3058720f"</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/certifi</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/certifi</string>
		</dict>
		<key>Frameworks/charset_normalizer/md.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kwY2uiP/E2DoKOvxd3WFQRDA8Oc=
			</data>
			<key>requirement</key>
			<string>cdhash H"a9e7a7a28b76c29227fe3d9e6ee3d0596fa7693a" or cdhash H"930636ba23ff1360e828ebf17775854110c0f0e7"</string>
		</dict>
		<key>Frameworks/charset_normalizer/md__mypyc.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			TlwDMSXdoZREoOPzzyc61+CkpRw=
			</data>
			<key>requirement</key>
			<string>cdhash H"99e3d2c3b9aa17dc6b031783155fe6f38bb077bc" or cdhash H"4e5c033125dda19444a0e3f3cf273ad7e0a4a51c"</string>
		</dict>
		<key>Frameworks/image_downloader</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/image_downloader</string>
		</dict>
		<key>Frameworks/importlib_metadata-8.6.1.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/importlib_metadata-8.6.1.dist-info</string>
		</dict>
		<key>Frameworks/lib-dynload/_asyncio.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BXq/W2DIP6Y4TYKG0bobcNTE2Bk=
			</data>
			<key>requirement</key>
			<string>cdhash H"057abf5b60c83fa6384d8286d1ba1b70d4c4d819"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			jSjl9MRGAOSF0cJVKnIuic2hgJk=
			</data>
			<key>requirement</key>
			<string>cdhash H"8d28e5f4c44600e485d1c2552a722e89cda18099"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hBPczY6f7hVIbzaXFvTLRe3nrMw=
			</data>
			<key>requirement</key>
			<string>cdhash H"8413dccd8e9fee15486f369716f4cb45ede7accc"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			efWWJz6pYtTRHBH1BonfGxfsqro=
			</data>
			<key>requirement</key>
			<string>cdhash H"79f596273ea962d4d11c11f50689df1b17ecaaba"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kawvtTHzWf/Yp+GEUwmP+tJGSlk=
			</data>
			<key>requirement</key>
			<string>cdhash H"91ac2fb531f359ffd8a7e18453098ffad2464a59"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			/ehomkVeCINrYmdtdeXaca63zlg=
			</data>
			<key>requirement</key>
			<string>cdhash H"fde8689a455e08836b62676d75e5da71aeb7ce58"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KjG9zkRb3O1ONOnckkSIbJaHrN4=
			</data>
			<key>requirement</key>
			<string>cdhash H"2a31bdce445bdced4e34e9dc9244886c9687acde"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pnrRpow0/N9H0NYFNGrR1aHG2y0=
			</data>
			<key>requirement</key>
			<string>cdhash H"a67ad1a68c34fcdf47d0d605346ad1d5a1c6db2d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Uj6F/oZjhnkrTMJYifcHFHQkb7s=
			</data>
			<key>requirement</key>
			<string>cdhash H"523e85fe866386792b4cc25889f7071474246fbb"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			JH6ejYOJOTqCgz81Rktwlf9uNF8=
			</data>
			<key>requirement</key>
			<string>cdhash H"247e9e8d8389393a82833f35464b7095ff6e345f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			yC5K+3kuKGVjMMfUX3502V4Ikkc=
			</data>
			<key>requirement</key>
			<string>cdhash H"c82e4afb792e28656330c7d45f7e74d95e089247"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			OeIi8xBclu2Wm8GfJlARyMMkrkA=
			</data>
			<key>requirement</key>
			<string>cdhash H"39e222f3105c96ed969bc19f265011c8c324ae40"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ctypes.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LgOfagi8E7sL0wY1vSv7utY8oOo=
			</data>
			<key>requirement</key>
			<string>cdhash H"2e039f6a08bc13bb0bd30635bd2bfbbad63ca0ea"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Fc/cX2cxsizzcwTrGYhnS1ppKfA=
			</data>
			<key>requirement</key>
			<string>cdhash H"15cfdc5f6731b22cf37304eb1988674b5a6929f0"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			YsDeNfShZXz5/n8ENp4psbodjog=
			</data>
			<key>requirement</key>
			<string>cdhash H"62c0de35f4a1657cf9fe7f04369e29b1ba1d8e88"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pHWDSver0rTpwx9sDr4cSN9yTws=
			</data>
			<key>requirement</key>
			<string>cdhash H"a475834af7abd2b4e9c31f6c0ebe1c48df724f0b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qw1u9sySAhKAhlf73ojstkvt4Ac=
			</data>
			<key>requirement</key>
			<string>cdhash H"ab0d6ef6cc920212808657fbde88ecb64bede007"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			QNv6rnTOZAM3BJzVTbLOx1wyg1w=
			</data>
			<key>requirement</key>
			<string>cdhash H"40dbfaae74ce640337049cd54db2cec75c32835c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			lHSDYyFEnLCBR/dpPI6Ct3LdGzY=
			</data>
			<key>requirement</key>
			<string>cdhash H"9474836321449cb08147f7693c8e82b772dd1b36"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			0/npX3QtAY+zk/3eB2rYYEgrXAA=
			</data>
			<key>requirement</key>
			<string>cdhash H"d3f9e95f742d018fb393fdde076ad860482b5c00"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LYqDbb+xWrf2IDIakch01pvg1po=
			</data>
			<key>requirement</key>
			<string>cdhash H"2d8a836dbfb15ab7f620321a91c874d69be0d69a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multiprocessing.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			iM7evS2rcf3XtqnSOHzklLKRAD0=
			</data>
			<key>requirement</key>
			<string>cdhash H"88cedebd2dab71fdd7b6a9d2387ce494b291003d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			6n3CyGb/kpl+HaTB4sC4AoZgSnY=
			</data>
			<key>requirement</key>
			<string>cdhash H"ea7dc2c866ff92997e1da4c1e2c0b80286604a76"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3HEaaKduLyeIy/YXk1yS5E2cDwo=
			</data>
			<key>requirement</key>
			<string>cdhash H"dc711a68a76e2f2788cbf617935c92e44d9c0f0a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixshmem.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3l0hJf5G5uHKk6IbZ8SjsPBvdzs=
			</data>
			<key>requirement</key>
			<string>cdhash H"de5d2125fe46e6e1ca93a21b67c4a3b0f06f773b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+HVfL/Hkc6i3t90TZ+379rW6ir8=
			</data>
			<key>requirement</key>
			<string>cdhash H"f8755f2ff1e473a8b7b7dd1367edfbf6b5ba8abf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			iKmEYPA8RApMMilwm1tvu33yNlA=
			</data>
			<key>requirement</key>
			<string>cdhash H"88a98460f03c440a4c3229709b5b6fbb7df23650"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			C1HbLnIHkr5daVQlai6dFOF3ZGM=
			</data>
			<key>requirement</key>
			<string>cdhash H"0b51db2e720792be5d6954256a2e9d14e1776463"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			S0W/JbiQQWsXrYmpiELXf4G7WEA=
			</data>
			<key>requirement</key>
			<string>cdhash H"4b45bf25b890416b17ad89a98842d77f81bb5840"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			V6/ZLgwa6IPMNjJ1lbWiGALUU8s=
			</data>
			<key>requirement</key>
			<string>cdhash H"57afd92e0c1ae883cc36327595b5a21802d453cb"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha256.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			9eWF6A3kpwLhKVx2thVqNCkeR9I=
			</data>
			<key>requirement</key>
			<string>cdhash H"f5e585e80de4a702e1295c76b6156a34291e47d2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Zqr1lpLmhZgJWbLUQcNKRUYL6Y4=
			</data>
			<key>requirement</key>
			<string>cdhash H"66aaf59692e685980959b2d441c34a45460be98e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha512.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wZUv+Xl452xwiXSphFQajDwZgv8=
			</data>
			<key>requirement</key>
			<string>cdhash H"c1952ff97978e76c708974a984541a8c3c1982ff"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			sToHrbtDY72NpoWgbMPUbZVZLB8=
			</data>
			<key>requirement</key>
			<string>cdhash H"b13a07adbb4363bd8da685a06cc3d46d95592c1f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			MRL6FJRgbOfSEiNfrnjpwcbZakI=
			</data>
			<key>requirement</key>
			<string>cdhash H"3112fa1494606ce7d212235fae78e9c1c6d96a42"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			nKCTy8+qDECHrX7ahl8rAE/Qb7A=
			</data>
			<key>requirement</key>
			<string>cdhash H"9ca093cbcfaa0c4087ad7eda865f2b004fd06fb0"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			RNd9ZNi5Pk2EPCN7G3h3muKhedE=
			</data>
			<key>requirement</key>
			<string>cdhash H"44d77d64d8b93e4d843c237b1b78779ae2a179d1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_typing.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			zO8eLvFK42mT3qhogUxIR83Qzas=
			</data>
			<key>requirement</key>
			<string>cdhash H"ccef1e2ef14ae36993dea868814c4847cdd0cdab"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Tn0bhffkfV/AylnkvtSe5VKdz50=
			</data>
			<key>requirement</key>
			<string>cdhash H"4e7d1b85f7e47d5fc0ca59e4bed49ee5529dcf9d"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			IMMm6LaMhxJGcDFdFxF8t5QZoNI=
			</data>
			<key>requirement</key>
			<string>cdhash H"20c326e8b68c87124670315d17117cb79419a0d2"</string>
		</dict>
		<key>Frameworks/lib-dynload/fcntl.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			bElXCStS4CFtuWl6AiVX9Yz95pQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"6c4957092b52e0216db9697a022557f58cfde694"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			HtWDGXj5nBzm73jHaCs6TuXxo9A=
			</data>
			<key>requirement</key>
			<string>cdhash H"1ed5831978f99c1ce6ef78c7682b3a4ee5f1a3d0"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			n2teVv6mTJh/0AO20F/6jLZ24HI=
			</data>
			<key>requirement</key>
			<string>cdhash H"9f6b5e56fea64c987fd003b6d05ffa8cb676e072"</string>
		</dict>
		<key>Frameworks/lib-dynload/mmap.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3LMC38drlcoJ8nPI4ru/86qeFz8=
			</data>
			<key>requirement</key>
			<string>cdhash H"dcb302dfc76b95ca09f273c8e2bbbff3aa9e173f"</string>
		</dict>
		<key>Frameworks/lib-dynload/pyexpat.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wy/3Wmno0Gwl4G0p2LCvUUOQ0O0=
			</data>
			<key>requirement</key>
			<string>cdhash H"c32ff75a69e8d06c25e06d29d8b0af514390d0ed"</string>
		</dict>
		<key>Frameworks/lib-dynload/readline.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			MBKbX3yButV/cBKuiC8rqwpyF2c=
			</data>
			<key>requirement</key>
			<string>cdhash H"30129b5f7c81bad57f7012ae882f2bab0a721767"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			h9fX7GPiphiv6Q0sQ8a1ZMOA4FU=
			</data>
			<key>requirement</key>
			<string>cdhash H"87d7d7ec63e2a618afe90d2c43c6b564c380e055"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			YaZy/GTPerPC1I54wwBQvv7GJjg=
			</data>
			<key>requirement</key>
			<string>cdhash H"61a672fc64cf7ab3c2d48e78c30050befec62638"</string>
		</dict>
		<key>Frameworks/lib-dynload/syslog.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			yx+BVfTLZv3+K9bQCu0wsNgdkuo=
			</data>
			<key>requirement</key>
			<string>cdhash H"cb1f8155f4cb66fdfe2bd6d00aed30b0d81d92ea"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			TfgQ17nEtnOL58TRRXdsJMR2sLw=
			</data>
			<key>requirement</key>
			<string>cdhash H"4df810d7b9c4b6738be7c4d145776c24c476b0bc"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			D/Ak7U2FkDKEngYvBehm1+aS6hA=
			</data>
			<key>requirement</key>
			<string>cdhash H"0ff024ed4d859032849e062f05e866d7e692ea10"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			***************************=
			</data>
			<key>requirement</key>
			<string>cdhash H"dec4fa82385e9e649bd53542f02de2be1afd2c45"</string>
		</dict>
		<key>Frameworks/libXau.6.0.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.0.0.dylib</string>
		</dict>
		<key>Frameworks/libcrypto.1.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			wMLOR5utAE0htGmgBNeDwjFyj6Y=
			</data>
			<key>requirement</key>
			<string>cdhash H"c0c2ce479bad004d21b469a004d783c231728fa6"</string>
		</dict>
		<key>Frameworks/libgcc_s.1.1.dylib</key>
		<dict>
			<key>symlink</key>
			<string>numpy/.dylibs/libgcc_s.1.1.dylib</string>
		</dict>
		<key>Frameworks/libgfortran.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>numpy/.dylibs/libgfortran.5.dylib</string>
		</dict>
		<key>Frameworks/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Frameworks/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Frameworks/libmpdec.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			kmZ/99xjQPINNdofFwxe5maGiKo=
			</data>
			<key>requirement</key>
			<string>cdhash H"92667ff7dc6340f20d35da1f170c5ee6668688aa"</string>
		</dict>
		<key>Frameworks/libopenblas64_.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>numpy/.dylibs/libopenblas64_.0.dylib</string>
		</dict>
		<key>Frameworks/libopenjp2.2.5.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.0.dylib</string>
		</dict>
		<key>Frameworks/libpyside6.abi3.6.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/libpyside6.abi3.6.5.dylib</string>
		</dict>
		<key>Frameworks/libquadmath.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>numpy/.dylibs/libquadmath.0.dylib</string>
		</dict>
		<key>Frameworks/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Frameworks/libshiboken6.abi3.6.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>shiboken6/libshiboken6.abi3.6.5.dylib</string>
		</dict>
		<key>Frameworks/libssl.1.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			YhMcv8S2VmjpDdFm9y31MaIpeSk=
			</data>
			<key>requirement</key>
			<string>cdhash H"62131cbfc4b65668e90dd166f72df531a2297929"</string>
		</dict>
		<key>Frameworks/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Frameworks/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Frameworks/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Frameworks/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Frameworks/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Frameworks/libz.1.2.13.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.2.13.dylib</string>
		</dict>
		<key>Frameworks/lxml/_elementpath.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+YIkAqJpYiAzjnsCBLCPlPhdwqI=
			</data>
			<key>requirement</key>
			<string>cdhash H"f9822402a2696220338e7b0204b08f94f85dc2a2"</string>
		</dict>
		<key>Frameworks/lxml/builder.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			78iKKJ0uLSbmt4TxHXEqE/N4QqY=
			</data>
			<key>requirement</key>
			<string>cdhash H"efc88a289d2e2d26e6b784f11d712a13f37842a6"</string>
		</dict>
		<key>Frameworks/lxml/etree.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			otxe84bMPebS+gBfUSA1RyJ/Oh0=
			</data>
			<key>requirement</key>
			<string>cdhash H"a2dc5ef386cc3de6d2fa005f51203547227f3a1d"</string>
		</dict>
		<key>Frameworks/lxml/html/clean.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wiTpyDVP+0o5o9S4tYFq5uEq9wI=
			</data>
			<key>requirement</key>
			<string>cdhash H"c224e9c8354ffb4a39a3d4b8b5816ae6e12af702"</string>
		</dict>
		<key>Frameworks/lxml/html/diff.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			brB8CxbmJ386D9/3ub/hHVXyfdM=
			</data>
			<key>requirement</key>
			<string>cdhash H"6eb07c0b16e6277f3a0fdff7b9bfe11d55f27dd3"</string>
		</dict>
		<key>Frameworks/lxml/isoschematron</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/lxml/isoschematron</string>
		</dict>
		<key>Frameworks/lxml/objectify.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			r3YGVGWsd/AbGe37RAfypqMD99Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"af76065465ac77f01b19edfb4407f2a6a303f7d4"</string>
		</dict>
		<key>Frameworks/lxml/sax.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			CH0EPjre9STwVi/NgvMuLtHdQgU=
			</data>
			<key>requirement</key>
			<string>cdhash H"087d043e3adef524f0562fcd82f32e2ed1dd4205"</string>
		</dict>
		<key>Frameworks/numpy/.dylibs</key>
		<dict>
			<key>symlink</key>
			<string>__dot__dylibs</string>
		</dict>
		<key>Frameworks/numpy/__dot__dylibs/libgcc_s.1.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Fj3Jus8M/oJXjryQcx414uNQtCg=
			</data>
			<key>requirement</key>
			<string>cdhash H"9c2310b58d8343a5e71cbb1afea0f8d07cbfa6c1" or cdhash H"163dc9bacf0cfe82578ebc90731e35e2e350b428"</string>
		</dict>
		<key>Frameworks/numpy/__dot__dylibs/libgfortran.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			fzyJq76X0fV0kqD/Fib2xA8xRro=
			</data>
			<key>requirement</key>
			<string>cdhash H"2488bc1dc42744eaecc7437285df70de7cd1e9f5" or cdhash H"7f3c89abbe97d1f57492a0ff1626f6c40f3146ba"</string>
		</dict>
		<key>Frameworks/numpy/__dot__dylibs/libopenblas64_.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			WHRXQGP+/lAgYTePgwIa1BhBlRo=
			</data>
			<key>requirement</key>
			<string>cdhash H"214748de8ff4dc051ba568dbdc105acdf2d77cec" or cdhash H"5874574063fefe502061378f83021ad41841951a"</string>
		</dict>
		<key>Frameworks/numpy/__dot__dylibs/libquadmath.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			cHQZ/7eApB1TGv0vXOOMs8zts7I=
			</data>
			<key>requirement</key>
			<string>cdhash H"aef341c71db68a12ff5d71513078e26cb041c19c" or cdhash H"707419ffb780a41d531afd2f5ce38cb3ccedb3b2"</string>
		</dict>
		<key>Frameworks/numpy/core/_multiarray_tests.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dDx049l2a08ibK4oW6Sben3TXFY=
			</data>
			<key>requirement</key>
			<string>cdhash H"c4904e4f3f4d2cf1ddec56759dedcc52965db06f" or cdhash H"743c74e3d9766b4f226cae285ba49b7a7dd35c56"</string>
		</dict>
		<key>Frameworks/numpy/core/_multiarray_umath.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			WIm47rYssm39apGJIvuM4QPJsao=
			</data>
			<key>requirement</key>
			<string>cdhash H"be3c8e2888f8b5b627c8295cbe5847ae79bb66a3" or cdhash H"5889b8eeb62cb26dfd6a918922fb8ce103c9b1aa"</string>
		</dict>
		<key>Frameworks/numpy/fft/_pocketfft_internal.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			R+fQxqTJ/DI7Aw77tMIeLBmOCiA=
			</data>
			<key>requirement</key>
			<string>cdhash H"13bcb4762e55b081f5f6c25e8a1f5348a024a046" or cdhash H"47e7d0c6a4c9fc323b030efbb4c21e2c198e0a20"</string>
		</dict>
		<key>Frameworks/numpy/linalg/_umath_linalg.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			IUDyjHmqZ02Jj7yh/dAydBYg0Gw=
			</data>
			<key>requirement</key>
			<string>cdhash H"723ef5eb905cdebadec35dbe885e12fa4a7973c2" or cdhash H"2140f28c79aa674d898fbca1fdd032741620d06c"</string>
		</dict>
		<key>Frameworks/numpy/linalg/lapack_lite.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			n1mqM2vGnoZH0lVfhRH27V69vqY=
			</data>
			<key>requirement</key>
			<string>cdhash H"4521616f6a3ccac84e63b99f6c76c3db57941ba1" or cdhash H"9f59aa336bc69e8647d2555f8511f6ed5ebdbea6"</string>
		</dict>
		<key>Frameworks/numpy/random/_bounded_integers.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			SMAExtcsAhIkTvPEwz93SZYfbYI=
			</data>
			<key>requirement</key>
			<string>cdhash H"5cb6808ecd39d5a95140e3a50932d482454c3cb3" or cdhash H"48c004c6d72c0212244ef3c4c33f7749961f6d82"</string>
		</dict>
		<key>Frameworks/numpy/random/_common.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			4SM+SwUckfVdtLhTzWxHFW+bKdo=
			</data>
			<key>requirement</key>
			<string>cdhash H"6d0485bedd5d8cdfe03f2f5bb8f8dffc47b2c152" or cdhash H"e1233e4b051c91f55db4b853cd6c47156f9b29da"</string>
		</dict>
		<key>Frameworks/numpy/random/_generator.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Squ5lzrf8rjyhbiGHJkzKIbKoE0=
			</data>
			<key>requirement</key>
			<string>cdhash H"a8e62a8b4b798dd14d921f96f96d37bcc8eff480" or cdhash H"4aabb9973adff2b8f285b8861c99332886caa04d"</string>
		</dict>
		<key>Frameworks/numpy/random/_mt19937.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			NdK9zILrZNPaEWLI3OcSzyeHqHY=
			</data>
			<key>requirement</key>
			<string>cdhash H"47f472039142b54b9a71bb4f2fa01c86bcbdb24e" or cdhash H"35d2bdcc82eb64d3da1162c8dce712cf2787a876"</string>
		</dict>
		<key>Frameworks/numpy/random/_pcg64.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			zu7FsJGpKVTn8MCJn1xT6zv4SuY=
			</data>
			<key>requirement</key>
			<string>cdhash H"2095dd3aa52d6fb8ff21e162faf599041b08b8da" or cdhash H"ceeec5b091a92954e7f0c0899f5c53eb3bf84ae6"</string>
		</dict>
		<key>Frameworks/numpy/random/_philox.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			mJvnPChHE6r20fn5otv0M9XPbZ0=
			</data>
			<key>requirement</key>
			<string>cdhash H"6ba11e2069422d1ac78dc670034d8ac36202a651" or cdhash H"989be73c284713aaf6d1f9f9a2dbf433d5cf6d9d"</string>
		</dict>
		<key>Frameworks/numpy/random/_sfc64.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			uVafLY7h7pAIpF1iFS99gI+uJyg=
			</data>
			<key>requirement</key>
			<string>cdhash H"a99547e5d909d51775c00e686b5c40ebeecc7136" or cdhash H"b9569f2d8ee1ee9008a45d62152f7d808fae2728"</string>
		</dict>
		<key>Frameworks/numpy/random/bit_generator.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			lYslFYgxzbz8RMgPPMk6zFjQtAI=
			</data>
			<key>requirement</key>
			<string>cdhash H"41d9ae78bb3d260b63dedac6c101d6cf5280ef8b" or cdhash H"958b25158831cdbcfc44c80f3cc93acc58d0b402"</string>
		</dict>
		<key>Frameworks/numpy/random/mtrand.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			bXAjkEcLyHmk3XQLRlemdLtkjeA=
			</data>
			<key>requirement</key>
			<string>cdhash H"f7dac0b94c65e1efd525bf8f8f80a8faf421b475" or cdhash H"6d702390470bc879a4dd740b4657a674bb648de0"</string>
		</dict>
		<key>Frameworks/shiboken6/Shiboken.abi3.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			50lF42ukElRv81QbrO60TSntNeQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"e74945e36ba412546ff3541baceeb44d29ed35e4"</string>
		</dict>
		<key>Frameworks/shiboken6/libshiboken6.abi3.6.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			xzyjs93DR7tLSUj8LiS+nQJchHg=
			</data>
			<key>requirement</key>
			<string>cdhash H"c73ca3b3ddc347bb4b4948fc2e24be9d025c8478"</string>
		</dict>
		<key>Frameworks/wheel-0.40.0.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/wheel-0.40.0.dist-info</string>
		</dict>
		<key>Resources/PIL</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/PIL</string>
		</dict>
		<key>Resources/PySide6/Qt/lib</key>
		<dict>
			<key>symlink</key>
			<string>../../../Frameworks/PySide6/Qt/lib</string>
		</dict>
		<key>Resources/PySide6/Qt/plugins</key>
		<dict>
			<key>symlink</key>
			<string>../../../Frameworks/PySide6/Qt/plugins</string>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_ar.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			sRMzxSDM2ON1zeIJhd9ALVhqGpXgeWMTu+ufcX1Nsi4=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_bg.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			HiLuEm7J92Ax2IAoWpB7OgxFEDkjGf9wDc3ulkq7sa0=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_ca.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			F6smI8f26N70Sz28tRdUQYWBfWXxKY3bEO9xE28jK9w=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_cs.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			xYWRj7HUghoFTqQL3PJ58nXKdsiKV5P0M9rGphGsfo8=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_da.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			9nc8ZyrLc/3vbgkgYNadHl3+6EABK6yRLR6S3ihxQ4Y=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_de.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			BAHDFqo7UKFy60Sk/x7DhJJAc77sfJKO+IjsSORhJog=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_en.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			mVm1ELFdGJN4SK0TAH4wRZ0umTxn5WS62/wY+TVpXIU=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_es.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			ZI7UUZvEXXpHeUeadibqFs+aoj6ToLF4VGzEe+oFkas=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_fa.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			P0yYnGspkNgMximUCCFLMWmJI7UvFUSDszcYdd6R+I4=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_fi.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			NC0Bx+6yNOQx0FBoIcTNA8BDMRXY1j/uM1e+IzGFiHk=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_fr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			yJajiJKpI0awM3k9VkQJkyNrI+R+BvOXSv2wVS9ig4c=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_gd.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Sd+FWgBKF5UDOK8xRkZvbfTVhSQQvQtY6oDg0CA6nSQ=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_gl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			IEoBrH3ra1uuGTr+y9HlDRjHO/fZS63rK7/fYSPE7ZM=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_he.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			NvAf/y8l4RbUt694CXt+SVIP1bf7b7Eg5AviKMBcBF8=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_ar.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			IiCIyXUtHMO6uYXvLcd+WueFeNzhimHsFbOfAuWIFj0=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_bg.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			y83R4LuuMy2A3bCihgVvF8gk+ijTU9f98S/JfZ9v4FQ=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_ca.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			jJi17iRuGDl9Dee9drKJF3OpdVU+xdzc5C+KaW5bNN4=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_cs.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			SsVvxj5ACUO6sT8dTEGFAhOJCOHUiMJK7mEx09F1Uqo=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_da.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			a7CSVSo5hocRn21SFF8Ev4Nzl3RG2PAMDcvVa5aCnw8=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_de.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			mWJSP7rp8eTDtcPBaGDQWSkcsw3F6+Wl7aTINqA/7R4=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_en.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			mVm1ELFdGJN4SK0TAH4wRZ0umTxn5WS62/wY+TVpXIU=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_es.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			/RayefjPaQd/delNkMnAeir/85SKV543ifX/teX0IC0=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_fr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			h17i3Nxk496oiNhASOOC05IaF18ShH3i5rHLEq2a3VA=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_gl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			c5miSGCZdHc/YIZsh7eOp9+8T3UDE9aS94hs12OIPJ8=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_hr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			jIzDxbGrKwdPgpNwAbiWYDQg73VZKzt4FyF3Ng3X6mE=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_hu.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			cPM7VpwpQvQcbWNOpqYcuNgOsscBG61I72266Wd5YNU=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_it.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			MW/o0IFeK0s5aJW+s47xpAQxkVteBU34D0wM1Vbybks=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_ja.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			rmA7LA1DTUDN5DP/y6ZfnuJ5eKnhkxYAe+f+eCpbi0c=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_ko.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			8RxkaU6ONOHSxGwaHRXWup8tt7Yd5P31TspauXfD4FI=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_nl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			RKig2vd10UNSGABS0T9iOBTXvfmOLFyAFEt790IU47A=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_nn.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			TIpyE5QgeOT3L1ur0mxBt3rOcBiXFKWLVQSn1a0Bk9w=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_pl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			zGy02MVAhiJGcvLknmI8jLfAwc1luNXs1C/JujpgZb0=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_pt_BR.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			F6FkBFwPxT0FB8/JDDlw/hr9+hxKjlyIHTT6H4aoIb8=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_ru.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			VXtkTm2l8exyDvk5ZWFwh+TR9AsklMxapSTPN5YQjec=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_sk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			9B4z4deQvQ0+sYDx+HW8GR/nR3NijyXCytleFALmaGc=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_sl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			LslV5mJAfrzY3NrlqqIeQQjgtbCu4OnbcSwnBylDU18=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_tr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			697KDP7nqUQd64ALq/2XxjvE5CHaiFxVs71Jcl66zSU=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_uk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			SXz8RzaEaS7kTXo3lej7InDFcGn9nrmKYV3Smrm+inw=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_zh_CN.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			4lbR9gNKWlUhxi8Sn5FzEhbkdPceHZyuo9PcsRtnDes=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_help_zh_TW.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Z+bfCmxl4PzzN5jftBf5u/DN5FErVunjFJql0Y97AZg=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_hr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			/J5BRvM749CbI0vgHlYjXTA32NgtZPBwC01KbiU6Yac=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_hu.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			wvpknxZ824AdHMDLacHZNZT9W/jAt+eblpk12lw+Gao=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_it.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			nnoYvCDr7sVLqq9ByRinRTRREnH3lt///Q+bnsMcaNM=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_ja.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			D1/lBFKUfR7pdVJJ2GA1S/9pdyebTElqW8d+W4nddF4=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_ko.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			HSVLU14cs1y8rYIQsVuJ9EiBWYJr81tFBGliY90QL6M=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_lt.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			R+tfl0Z992kmFCHVSlvqETHJ+5tjiHkdOLtldDNbZL8=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_lv.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			rd7KKFKCM4KU8kylDTfQil9o03hdCpSvht1OyPKjtEk=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_nl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Oehwy9JcD5f6mxgo1eoe2YSYV6LibFMhs/E8dr6PBUg=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_nn.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			TlROI/der+FVPciWlz2yO1eVzSQ3htXGU8wPNqRsp08=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_pl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			5okK+gyvD22wJI5MlPYBa47IvCZvHg6bjwHo6pEs+Wg=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_pt_BR.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			DiTFjjUnGFP4zfs/D3sHQbWppYjkPN7GKuhaOEJ3Hgo=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_pt_PT.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			LB57v1FopktDdS3UxUdgHAvebWEPhnH6PjrzhZfoR4M=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_ru.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			GziOLW0b1AVgPnlOSYxtfsX1pa2BkkNUb2OZGB2jZ1g=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_sk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			hAw7Acid+Xb8HS5b/G/IS2Vma4o860y/WqDCKvPSo4M=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_sl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			xE4DE6lBTMDkkLZbDANvoRvKlZNTsiiIZUe8LISSA08=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_sv.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			S0tv9/0jfJ2gMBtJRhMuaGU9Fetfrzjkxfv+uxLdl/c=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_tr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			WeigxBG+OwgCpoVhsJDxggmda3XE/kbzmOVfEVKOEbk=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_uk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			BT7B9nfE26SMapfrKPT8eMFREyTRhD1/TOwsfACmYUU=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_zh_CN.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			wOKdMslUCn54ZDt0IU5jPFb2qxHwGOUWYp8sEbH7Cfw=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qt_zh_TW.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			JOImlUSRaJVJoaGZDbO4y3iIgAjYFjsv0kQrdyttG18=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_ar.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			4D/mjYMgFUNpj9f+Jn3V38W/0ZUUfnT/LxmsNJFAEmM=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_bg.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			5Eisnj8Wwp6yevMBLv4hBS2qePq/s0zW3/L2nuO9PNs=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_ca.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			QlAj8bUwRW0LoBLjK4TaqqWqO8OF2aB253UTk9s5PXc=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_cs.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			AwKLQt9UeScDceTDvcffL1bLvm3alWooZKxvZBWGH+g=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_da.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			fR5cozELVNEEwZvyq9QCs45YTocDmnDhU8Spr3SyXCI=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_de.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			x7d3v4wcunC/1L0hzecjLmI2QGCvOkkjBya1lVOE60U=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_en.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			mVm1ELFdGJN4SK0TAH4wRZ0umTxn5WS62/wY+TVpXIU=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_es.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			T/2la6O7VBSrBILR3eZKbyJuNIj2t/PxGhUOAfU/pMg=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_fa.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			945Ztb3VhhgamZA0ukGIaO0X/pwFcH+2XlI/cOkiU9I=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_fi.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			5H/hNxPhhNB/pEld3gxYmw6PVi6RV0o1WKk2NEOk+nI=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_fr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			MUJDpVwyZ5rUc/3+SNx9ZulzrQ/IvyFkSAxY3dtQwac=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_gd.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Y7Q53UQTmqOu1UwuvgP6m8d/IsFO2Puo7/JghEW7Iz0=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_he.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			4evKFq/omUNW+BygB/vbnd+GWEIBD+kIkj2HO2h8rT8=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_hr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			u57smpplLBNA3HXrLnSb5Q3wD4hbPWkA38dnmcRbJE0=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_hu.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			xhtnu50ehPCrB5K2UY/gVUFKaORNDHvHyGJ3OAD6gpk=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_it.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			eAjtVCp4M8S9lOyL37vKALtPL+FZebkSHOMkkyNocwA=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_ja.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			K5WBxpuXX1m+UFsSfNPBnUA+89EuDoOG+5cjgiPpwd8=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_ko.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			AXntGxNuHLP1gzUeqixUW6PYOm7j+CwyUFkmoaX18YM=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_lv.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			hG4EdXOuQMg2ccO6f3PifvwkuYyCcB2g35lz5XQXi7I=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_nl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			9hUTvNu2rYAFhflDepXQGKqxf6Eu5BSqMUYv8nnYJA4=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_nn.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			mDc0tAqk0lBCG6DRYUQWy4tCTW8UD0p9v4sR5l7vY9w=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_pl.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			zpkDKjsL+KutdYiVzCKDcIjq2Z/S0lFOLRgGkwgc/lc=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_pt_BR.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			g0T1vDM8RdN8/UJaCRJOsR8SQ58zc37AuGg9BKQWdG4=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_ru.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			PaZgVmj5F40RqDjEUVR4CE3PtPnPIvmdepK0ktucIks=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_sk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			1YauLDFAdM85hBf97LQHCdVHjf6wpnwv5g1Qnum1ntc=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_tr.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			agv25w55IMKxk+dukvePMVk2lV07BqwDnZF/LgbEMoE=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_uk.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			Ubj/VbN9xZB9Y3qN3aEvvoFoUrAkTHTrTw+4SGenhuA=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_zh_CN.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			mP1Ll9vsivXN0V+FoWwz78dzAbGusEadqvwEuasvNXA=
			</data>
		</dict>
		<key>Resources/PySide6/Qt/translations/qtbase_zh_TW.qm</key>
		<dict>
			<key>hash2</key>
			<data>
			IQErK0J/jQnTHOnK5YLKJ5VbR4yj3C7BijBo2AhjjGo=
			</data>
		</dict>
		<key>Resources/PySide6/QtCore.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PySide6/QtCore.abi3.so</string>
		</dict>
		<key>Resources/PySide6/QtDBus.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PySide6/QtDBus.abi3.so</string>
		</dict>
		<key>Resources/PySide6/QtGui.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PySide6/QtGui.abi3.so</string>
		</dict>
		<key>Resources/PySide6/QtNetwork.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PySide6/QtNetwork.abi3.so</string>
		</dict>
		<key>Resources/PySide6/QtWidgets.abi3.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PySide6/QtWidgets.abi3.so</string>
		</dict>
		<key>Resources/PySide6/libpyside6.abi3.6.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PySide6/libpyside6.abi3.6.5.dylib</string>
		</dict>
		<key>Resources/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.11/Python</string>
		</dict>
		<key>Resources/Python.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python.framework</string>
		</dict>
		<key>Resources/QtCore</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtCore.framework/Versions/A/QtCore</string>
		</dict>
		<key>Resources/QtDBus</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtDBus.framework/Versions/A/QtDBus</string>
		</dict>
		<key>Resources/QtGui</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtGui.framework/Versions/A/QtGui</string>
		</dict>
		<key>Resources/QtNetwork</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtNetwork.framework/Versions/A/QtNetwork</string>
		</dict>
		<key>Resources/QtOpenGL</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtOpenGL.framework/Versions/A/QtOpenGL</string>
		</dict>
		<key>Resources/QtPdf</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtPdf.framework/Versions/A/QtPdf</string>
		</dict>
		<key>Resources/QtQml</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtQml.framework/Versions/A/QtQml</string>
		</dict>
		<key>Resources/QtQmlModels</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtQmlModels.framework/Versions/A/QtQmlModels</string>
		</dict>
		<key>Resources/QtQuick</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtQuick.framework/Versions/A/QtQuick</string>
		</dict>
		<key>Resources/QtSvg</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtSvg.framework/Versions/A/QtSvg</string>
		</dict>
		<key>Resources/QtVirtualKeyboard</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/QtVirtualKeyboard</string>
		</dict>
		<key>Resources/QtWidgets</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/Qt/lib/QtWidgets.framework/Versions/A/QtWidgets</string>
		</dict>
		<key>Resources/_brotli.cpython-311-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/_brotli.cpython-311-darwin.so</string>
		</dict>
		<key>Resources/_cffi_backend.cpython-311-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/_cffi_backend.cpython-311-darwin.so</string>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			E42Rvj9PUfBKcCLq3xTRZ1KXIKCOLnDuTsWVTUXBHEo=
			</data>
		</dict>
		<key>Resources/certifi/cacert.pem</key>
		<dict>
			<key>hash2</key>
			<data>
			LBHDzgj/xA05AxnHK8ENT5COnGNElNZe0svFUHMf1SQ=
			</data>
		</dict>
		<key>Resources/certifi/py.typed</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/charset_normalizer</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/charset_normalizer</string>
		</dict>
		<key>Resources/icon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			mq0uPqEcDtwwkHDhNRw/qHhajfesR+K/U7ks2T2Mp1c=
			</data>
		</dict>
		<key>Resources/image_downloader/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			HgdAhAD8qhUTgQtgTXRlZEAvdsWljAyFVnhwHsoKYxc=
			</data>
		</dict>
		<key>Resources/image_downloader/__pycache__/__init__.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			Vc5+yj/cPrMPKfG2fa7HjYbgZcR3KR/5vqaSlJiKwzE=
			</data>
		</dict>
		<key>Resources/image_downloader/__pycache__/downloader.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			D4T5lh3xJwhVEKjljtA5M1XomNceTGIkuwuZJNkQrYg=
			</data>
		</dict>
		<key>Resources/image_downloader/__pycache__/image_processor.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			/ZwKagVo+KCgoMSZRd8SXn4clsZhznRWcFGg0Pg6XOs=
			</data>
		</dict>
		<key>Resources/image_downloader/__pycache__/logger.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			k/vF1XKde2YmWyV4K/FXzAngIj0800A2vs+XQfTg0mU=
			</data>
		</dict>
		<key>Resources/image_downloader/__pycache__/utils.cpython-311.pyc</key>
		<dict>
			<key>hash2</key>
			<data>
			IZWkK9oUZldrzLkJ1AAlIXXMyGp9R56bbszT+YcNSso=
			</data>
		</dict>
		<key>Resources/image_downloader/downloader.py</key>
		<dict>
			<key>hash2</key>
			<data>
			sjjXGG3BqnPmwaiQjV5vVuUcS8ODogApOIsXSTbup6U=
			</data>
		</dict>
		<key>Resources/image_downloader/image_processor.py</key>
		<dict>
			<key>hash2</key>
			<data>
			VEnAxMY1fe5h824Ywj8hmprqlnlaQlBrfPhAbni8gYw=
			</data>
		</dict>
		<key>Resources/image_downloader/logger.py</key>
		<dict>
			<key>hash2</key>
			<data>
			x1Qufotm50w8ruPgqUyiXu1qwnFYjmBpJaFJvDBAb94=
			</data>
		</dict>
		<key>Resources/image_downloader/utils.py</key>
		<dict>
			<key>hash2</key>
			<data>
			AX9J7/iNyCB2cVHtrGtUESDXBXLEDs3TQKY329dPvIo=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			F24ATbamOm1kp40IQGodtMLWMrH+fVEksdySlZk/KAY=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			Tn678RTz7HdfOne8NT+O1MqJL6bd1mzHoWmzGvbFbo4=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			In9FTNxeP60KnTkGw7wk6mJPYd/dQSjEZmXdBdMCI+8=
			</data>
		</dict>
		<key>Resources/importlib_metadata-8.6.1.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			CO3fD9yylANiXkrMo4qHLV/mqXL2sC5JFKgt1yWAT+A=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libXau.6.0.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.0.0.dylib</string>
		</dict>
		<key>Resources/libcrypto.1.1.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libcrypto.1.1.dylib</string>
		</dict>
		<key>Resources/libgcc_s.1.1.dylib</key>
		<dict>
			<key>symlink</key>
			<string>numpy/.dylibs/libgcc_s.1.1.dylib</string>
		</dict>
		<key>Resources/libgfortran.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>numpy/.dylibs/libgfortran.5.dylib</string>
		</dict>
		<key>Resources/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Resources/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Resources/libmpdec.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libmpdec.3.dylib</string>
		</dict>
		<key>Resources/libopenblas64_.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>numpy/.dylibs/libopenblas64_.0.dylib</string>
		</dict>
		<key>Resources/libopenjp2.2.5.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.0.dylib</string>
		</dict>
		<key>Resources/libpyside6.abi3.6.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PySide6/libpyside6.abi3.6.5.dylib</string>
		</dict>
		<key>Resources/libquadmath.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>numpy/.dylibs/libquadmath.0.dylib</string>
		</dict>
		<key>Resources/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Resources/libshiboken6.abi3.6.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>shiboken6/libshiboken6.abi3.6.5.dylib</string>
		</dict>
		<key>Resources/libssl.1.1.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libssl.1.1.dylib</string>
		</dict>
		<key>Resources/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Resources/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Resources/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Resources/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Resources/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Resources/libz.1.2.13.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.2.13.dylib</string>
		</dict>
		<key>Resources/lxml/_elementpath.cpython-311-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/lxml/_elementpath.cpython-311-darwin.so</string>
		</dict>
		<key>Resources/lxml/builder.cpython-311-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/lxml/builder.cpython-311-darwin.so</string>
		</dict>
		<key>Resources/lxml/etree.cpython-311-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/lxml/etree.cpython-311-darwin.so</string>
		</dict>
		<key>Resources/lxml/html</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/lxml/html</string>
		</dict>
		<key>Resources/lxml/isoschematron/resources/rng/iso-schematron.rng</key>
		<dict>
			<key>hash2</key>
			<data>
			VsWxPyi3iViJDDbjJJw0wWkEHkLrz9zoCA8zJLor9N4=
			</data>
		</dict>
		<key>Resources/lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl</key>
		<dict>
			<key>hash2</key>
			<data>
			ObebsB8Wt+d3uIA/U5NU85TpnQ3PxPX38TdOAqosMac=
			</data>
		</dict>
		<key>Resources/lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl</key>
		<dict>
			<key>hash2</key>
			<data>
			QweRrIIM+zFcgg98GXA2CaWfIbgVE0XKEeYSfvv67A0=
			</data>
		</dict>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl</key>
		<dict>
			<key>hash2</key>
			<data>
			xSZ/Ekq/I+62ZpiE5AqYYHwFW/qh855zt9V4/s7rbkY=
			</data>
		</dict>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl</key>
		<dict>
			<key>hash2</key>
			<data>
			x42QJ+dxQ1waPzydsCoQnp2Xj15y53nW43O7BuoDRHk=
			</data>
		</dict>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl</key>
		<dict>
			<key>hash2</key>
			<data>
			Tr9BnO6pzjVWwhqJfm10UlvAy95EgfSCz2iMlrVGT6Q=
			</data>
		</dict>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl</key>
		<dict>
			<key>hash2</key>
			<data>
			ue8q/88X4e/jsJizo31GRNBxNhdxkEE9fY20oq0Iqwk=
			</data>
		</dict>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl</key>
		<dict>
			<key>hash2</key>
			<data>
			BBAdsVSi5zAzeGepuN6gS1saQINDqITXKplmmj4dTWg=
			</data>
		</dict>
		<key>Resources/lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			OGLiFswuLJEW5EPYKOeoauuCJFEtVa6jyzBE1OcJI98=
			</data>
		</dict>
		<key>Resources/lxml/objectify.cpython-311-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/lxml/objectify.cpython-311-darwin.so</string>
		</dict>
		<key>Resources/lxml/sax.cpython-311-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/lxml/sax.cpython-311-darwin.so</string>
		</dict>
		<key>Resources/numpy</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/numpy</string>
		</dict>
		<key>Resources/shiboken6</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/shiboken6</string>
		</dict>
		<key>Resources/wheel-0.40.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/wheel-0.40.0.dist-info/LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			MMI2GGeRCPPo6h0qZYx8pBe9/IkcmO8aifpP8MmChlQ=
			</data>
		</dict>
		<key>Resources/wheel-0.40.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			lgTWaV1e5PLefpgNsml/Q9IhZOPDXqIMvI/1wEUxU1Y=
			</data>
		</dict>
		<key>Resources/wheel-0.40.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			gX2D9VW5o1+ojuoh6elHEX5Nt34v4L29lYU7A5UICRA=
			</data>
		</dict>
		<key>Resources/wheel-0.40.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/wheel-0.40.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			rSgq/JpHF9fHR1lx53qwg/1+2LypZE/qmcuXbVUq948=
			</data>
		</dict>
		<key>Resources/wheel-0.40.0.dist-info/direct_url.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DKc2yemP36qzJW9KoHOj250yKB62BoTnZlx3QjFiPxM=
			</data>
		</dict>
		<key>Resources/wheel-0.40.0.dist-info/entry_points.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			rTY1BbkPHhkGMm4Q3F0pIzJBzW2kMxoG1oriffvGdA0=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
