import os
import re
import requests
import logging
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from PySide6.QtCore import QObject, Signal
from PIL import Image
import io

class ImageDownloader(QObject):
    progress_updated = Signal(str, int, int)  # 网页URL, 当前进度, 总数
    download_completed = Signal(str, str)  # 网页URL, 保存路径
    download_error = Signal(str, str)  # 网页URL, 错误信息

    def __init__(self):
        super().__init__()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.logger = logging.getLogger()

    def get_page_title(self, soup):
        """获取网页标题"""
        title = soup.title.string if soup.title else "未命名"
        # 清理标题中的非法字符
        title = re.sub(r'[\\/:*?"<>|]', '_', title)
        self.logger.debug(f"获取到页面标题: {title}")
        return title.strip()

    def get_unique_save_path(self, base_path, download_count):
        """获取唯一的保存路径"""
        if download_count <= 1:
            return base_path
        
        # 如果已经下载过，添加序号
        dir_name = os.path.basename(base_path)
        parent_dir = os.path.dirname(base_path)
        new_dir_name = f"{dir_name}_{download_count}"
        new_path = os.path.join(parent_dir, new_dir_name)
        self.logger.debug(f"生成新的保存路径: {new_path}")
        return new_path

def extract_images(self, soup, base_url, excluded_formats):
    """提取页面中的所有图片URL，保持网页中的原始顺序"""
    images = []
    # 获取所有可能包含图片的元素
    for element in soup.find_all(['img', 'div', 'figure']):
        # 如果是img标签
        if element.name == 'img':
            img_url = element.get('src') or element.get('data-src') or element.get('data-original')
            if img_url:
                # 转换为绝对URL
                img_url = urljoin(base_url, img_url)
                
                # 检查图片格式
                ext = os.path.splitext(img_url)[1].lower().lstrip('.')
                if ext in excluded_formats and excluded_formats[ext]:
                    self.logger.debug(f"跳过格式为 {ext} 的图片: {img_url}")
                    continue
                
                # 获取alt文本作为文件名
                alt_text = element.get('alt', '')
                # 记录元素在DOM中的位置
                position = len(images)
                images.append((img_url, alt_text, position))
                self.logger.debug(f"找到图片: {img_url} (alt: {alt_text}, position: {position})")
        # 如果是div或figure标签，查找其中的img标签
        else:
            for img in element.find_all('img'):
                img_url = img.get('src') or img.get('data-src') or img.get('data-original')
                if img_url:
                    # 转换为绝对URL
                    img_url = urljoin(base_url, img_url)
                    
                    # 检查图片格式
                    ext = os.path.splitext(img_url)[1].lower().lstrip('.')
                    if ext in excluded_formats and excluded_formats[ext]:
                        self.logger.debug(f"跳过格式为 {ext} 的图片: {img_url}")
                        continue
                    
                    # 获取alt文本作为文件名
                    alt_text = img.get('alt', '')
                    # 记录元素在DOM中的位置
                    position = len(images)
                    images.append((img_url, alt_text, position))
                    self.logger.debug(f"找到图片: {img_url} (alt: {alt_text}, position: {position})")
    
    # 按照DOM中的位置排序
    images.sort(key=lambda x: x[2])
    # 移除位置信息，只保留URL和alt文本
    images = [(url, alt) for url, alt, _ in images]
    
    self.logger.info(f"共找到 {len(images)} 张图片")
    return images

    def convert_image_format(self, image_data, target_format):
        """转换图片格式"""
        try:
            # 从二进制数据创建图片对象
            img = Image.open(io.BytesIO(image_data))
            
            # 如果是PNG格式且需要转换为JPG，需要处理透明背景
            if target_format.lower() == 'jpg' and img.mode in ('RGBA', 'LA'):
                self.logger.debug("处理PNG透明背景")
                background = Image.new('RGB', img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1])
                img = background
            
            # 转换为RGB模式（如果需要）
            if img.mode not in ('RGB', 'L'):
                self.logger.debug(f"转换图片模式从 {img.mode} 到 RGB")
                img = img.convert('RGB')
            
            # 保存为新的格式
            output = io.BytesIO()
            save_format = target_format.upper()
            if save_format == 'JPG':
                save_format = 'JPEG'
            img.save(output, format=save_format, quality=95)
            self.logger.debug(f"图片格式转换成功: {save_format}")
            return output.getvalue()
        except Exception as e:
            self.logger.error(f"转换图片格式失败: {str(e)}")
            return None

    def download_image(self, img_url, save_path, index, total_images, save_format):
        """下载单个图片"""
        try:
            self.logger.info(f"开始下载图片 {index}/{total_images}: {img_url}")
            response = requests.get(img_url, headers=self.headers, stream=True)
            response.raise_for_status()
            
            # 获取原始图片数据
            image_data = response.content
            
            # 转换图片格式
            if save_format:
                self.logger.debug(f"转换图片格式为: {save_format}")
                converted_data = self.convert_image_format(image_data, save_format)
                if converted_data:
                    image_data = converted_data
            
            # 生成文件名
            filename = f"{index:03d}.{save_format.lower()}"
            filepath = os.path.join(save_path, filename)
            
            # 保存图片
            with open(filepath, 'wb') as f:
                f.write(image_data)
            self.logger.info(f"图片保存成功: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"下载图片失败: {img_url}, 错误: {str(e)}")
            return False

    def download_from_url(self, url, save_dir, excluded_formats, save_format, download_count=1):
        """从URL下载所有图片"""
        try:
            self.logger.info(f"开始从URL下载图片: {url}")
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'lxml')
            
            # 获取页面标题并创建保存目录
            title = self.get_page_title(soup)
            base_save_path = os.path.join(save_dir, title)
            save_path = self.get_unique_save_path(base_save_path, download_count)
            os.makedirs(save_path, exist_ok=True)
            self.logger.info(f"创建保存目录: {save_path}")
            
            # 提取并下载图片
            images = self.extract_images(soup, url, excluded_formats)
            total_images = len(images)
            
            if total_images == 0:
                self.logger.warning(f"未找到任何图片: {url}")
                self.download_error.emit(url, "未找到任何图片")
                return False
            
            for index, (img_url, _) in enumerate(images, 1):
                self.download_image(img_url, save_path, index, total_images, save_format)
                self.progress_updated.emit(url, index, total_images)
            
            self.logger.info(f"下载完成: {url} -> {save_path}")
            self.download_completed.emit(url, save_path)
            return True
            
        except Exception as e:
            self.logger.error(f"下载过程发生错误: {url}, 错误: {str(e)}")
            self.download_error.emit(url, str(e))
            return False 