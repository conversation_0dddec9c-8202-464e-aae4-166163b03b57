import sys
import os
import json
import datetime
import logging
import requests
from bs4 import BeautifulSoup
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLineEdit, QPushButton, QListWidget,
                             QProgressBar, QFileDialog, QMessageBox, QLabel,
                             QGroupBox, QCheckBox, QComboBox, QListWidgetItem,
                             QTabWidget, QScrollArea, QFrame, QTextEdit)
from PySide6.QtCore import Qt, QThread, Signal, Slot, QTimer
from PySide6.QtGui import QClipboard, QFont
from image_downloader import ImageDownloader

def get_app_data_dir():
    """获取应用程序数据目录"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的应用
        app_dir = os.path.dirname(sys.executable)
        data_dir = os.path.join(os.path.expanduser("~"), "Library", "Application Support", "网页图片抓取工具")
    else:
        # 如果是开发环境
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data")

    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    return data_dir

# 配置日志
def setup_logging():
    """配置日志系统"""
    # 创建日志目录
    log_dir = os.path.join(get_app_data_dir(), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, f"spider_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    # 创建日志处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    console_handler = logging.StreamHandler()

    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 配置根日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

class LogHandler(logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget
        self.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

    def emit(self, record):
        msg = self.format(record)
        self.text_widget.append(msg)

class DownloadWorker(QThread):
    def __init__(self, url, save_dir, excluded_formats, save_format):
        super().__init__()
        self.url = url
        self.save_dir = save_dir
        self.excluded_formats = excluded_formats
        self.save_format = save_format
        self.downloader = ImageDownloader()
        self.downloader.progress_updated.connect(self.progress_updated)
        self.downloader.download_completed.connect(self.download_completed)
        self.downloader.download_error.connect(self.download_error)
        self.is_running = True

    progress_updated = Signal(str, int, int)
    download_completed = Signal(str, str)
    download_error = Signal(str, str)

    def run(self):
        if self.is_running:
            self.downloader.download_from_url(self.url, self.save_dir, self.excluded_formats, self.save_format)

    def stop(self):
        self.is_running = False
        self.wait()

class HistoryItem:
    def __init__(self, url, timestamp=None):
        self.url = url
        self.timestamp = timestamp or datetime.datetime.now()
        self.download_count = 0  # 添加下载次数计数

    def to_dict(self):
        return {
            'url': self.url,
            'timestamp': self.timestamp.isoformat(),
            'download_count': self.download_count
        }

    @classmethod
    def from_dict(cls, data):
        item = cls(
            url=data['url'],
            timestamp=datetime.datetime.fromisoformat(data['timestamp'])
        )
        item.download_count = data.get('download_count', 0)
        return item

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("网页图片一键抓取工具")
        self.setMinimumSize(1000, 700)

        # 初始化日志
        self.logger = setup_logging()

        # 初始化变量
        self.save_dir = os.path.expanduser("~/Downloads")
        self.history_file = os.path.join(get_app_data_dir(), "download_history.json")
        self.history = self.load_history()
        self.workers = []
        self.is_closing = False
        self.excluded_formats = {
            'png': True,
            'gif': True,
            'svg': True
        }
        self.save_format = 'webp'
        self.progress_bars = {}  # 初始化进度条字典

        # 批量下载相关变量
        self.batch_download_queue = []  # 批量下载队列
        self.batch_download_timer = QTimer()  # 批量下载定时器
        self.batch_download_timer.timeout.connect(self.process_next_batch_download)
        self.batch_download_timer.setSingleShot(True)
        self.batch_download_interval = 500  # 每个下载任务间隔500ms
        self.max_concurrent_downloads = 3  # 最大并发下载数

        # 记录启动信息
        self.logger.info("应用程序启动")
        self.logger.info(f"数据目录: {get_app_data_dir()}")
        self.logger.info(f"保存目录: {self.save_dir}")

        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)

        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # 创建主页标签页
        home_tab = QWidget()
        home_layout = QVBoxLayout(home_tab)

        # URL输入区域
        url_group = QGroupBox("URL输入")
        url_layout = QVBoxLayout()

        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("请输入网页地址（支持多个地址，用分号;分隔）")
        self.url_input.setMinimumHeight(30)
        url_layout.addWidget(self.url_input)

        add_btn = QPushButton("开始下载")
        add_btn.setMinimumHeight(35)
        add_btn.clicked.connect(self.add_url)
        url_layout.addWidget(add_btn)

        url_group.setLayout(url_layout)
        home_layout.addWidget(url_group)

        # 图片格式设置区域
        format_settings_layout = QHBoxLayout()

        # 图片格式过滤选项
        format_group = QGroupBox("图片格式过滤")
        format_layout = QHBoxLayout()

        self.format_checkboxes = {}
        for fmt in ['jpg', 'png', 'gif', 'webp', 'svg']:
            checkbox = QCheckBox(fmt.upper())
            checkbox.setChecked(not self.excluded_formats.get(fmt, False))
            checkbox.stateChanged.connect(self.update_excluded_formats)
            self.format_checkboxes[fmt] = checkbox
            format_layout.addWidget(checkbox)

        format_group.setLayout(format_layout)
        format_settings_layout.addWidget(format_group)

        # 保存格式选择
        save_format_group = QGroupBox("保存格式")
        save_format_layout = QHBoxLayout()

        self.save_format_combo = QComboBox()
        self.save_format_combo.addItems(['webp', 'jpg', 'png'])
        self.save_format_combo.setCurrentText(self.save_format)
        self.save_format_combo.currentTextChanged.connect(self.update_save_format)

        save_format_layout.addWidget(self.save_format_combo)
        save_format_group.setLayout(save_format_layout)
        format_settings_layout.addWidget(save_format_group)

        home_layout.addLayout(format_settings_layout)

        # 保存目录设置
        dir_group = QGroupBox("保存设置")
        dir_layout = QHBoxLayout()
        self.dir_label = QLabel(f"保存目录: {self.save_dir}")
        dir_layout.addWidget(self.dir_label)

        change_dir_btn = QPushButton("更改目录")
        change_dir_btn.clicked.connect(self.change_save_dir)
        dir_layout.addWidget(change_dir_btn)

        dir_group.setLayout(dir_layout)
        home_layout.addWidget(dir_group)

        # 添加主页标签页
        self.tab_widget.addTab(home_tab, "主页")

        # 创建下载列表标签页
        download_tab = QWidget()
        download_layout = QVBoxLayout(download_tab)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_widget = QWidget()
        self.progress_layout = QVBoxLayout(scroll_widget)
        scroll_area.setWidget(scroll_widget)
        download_layout.addWidget(scroll_area)

        # 添加下载列表标签页
        self.tab_widget.addTab(download_tab, "下载列表")

        # 创建历史记录标签页
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)

        history_label = QLabel("历史记录")
        history_label.setFont(QFont("Arial", 12, QFont.Bold))
        history_layout.addWidget(history_label)

        self.history_list = QListWidget()
        self.history_list.setMinimumHeight(400)
        self.history_list.itemDoubleClicked.connect(self.on_history_double_clicked)
        history_layout.addWidget(self.history_list)

        # 历史记录按钮
        history_btn_layout = QHBoxLayout()
        clear_btn = QPushButton("清空历史")
        clear_btn.clicked.connect(self.clear_history)
        history_btn_layout.addWidget(clear_btn)

        delete_btn = QPushButton("删除选中")
        delete_btn.clicked.connect(self.delete_selected_history)
        history_btn_layout.addWidget(delete_btn)

        copy_btn = QPushButton("复制选中")
        copy_btn.clicked.connect(self.copy_selected_history)
        history_btn_layout.addWidget(copy_btn)

        history_layout.addLayout(history_btn_layout)

        # 添加历史记录标签页
        self.tab_widget.addTab(history_tab, "历史记录")

        # 创建日志标签页
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)

        # 创建日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Courier New", 10))
        log_layout.addWidget(self.log_text)

        # 添加日志处理器
        log_handler = LogHandler(self.log_text)
        self.logger.addHandler(log_handler)

        # 添加日志标签页
        self.tab_widget.addTab(log_tab, "运行日志")

        # 创建地址解析标签页
        url_parser_tab = QWidget()
        url_parser_layout = QVBoxLayout(url_parser_tab)

        # URL输入区域
        parser_url_group = QGroupBox("网页地址输入")
        parser_url_layout = QVBoxLayout()

        self.parser_url_input = QLineEdit()
        self.parser_url_input.setPlaceholderText("请输入需要解析的网页地址")
        self.parser_url_input.setMinimumHeight(30)
        parser_url_layout.addWidget(self.parser_url_input)

        parse_btn = QPushButton("提取地址")
        parse_btn.setMinimumHeight(35)
        parse_btn.clicked.connect(self.parse_urls)
        parser_url_layout.addWidget(parse_btn)

        parser_url_group.setLayout(parser_url_layout)
        url_parser_layout.addWidget(parser_url_group)

        # 结果显示区域
        result_group = QGroupBox("提取结果")
        result_layout = QVBoxLayout()

        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setFont(QFont("Courier New", 10))
        result_layout.addWidget(self.result_text)

        # 按钮区域
        button_layout = QHBoxLayout()

        copy_btn = QPushButton("复制全部")
        copy_btn.clicked.connect(self.copy_all_results)
        button_layout.addWidget(copy_btn)

        save_btn = QPushButton("保存到文件")
        save_btn.clicked.connect(self.save_results)
        button_layout.addWidget(save_btn)

        # 添加一键下载按钮
        batch_download_btn = QPushButton("批量一键下载")
        batch_download_btn.clicked.connect(self.batch_download_from_results)
        batch_download_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        button_layout.addWidget(batch_download_btn)

        result_layout.addLayout(button_layout)
        result_group.setLayout(result_layout)
        url_parser_layout.addWidget(result_group)

        # 添加地址解析标签页
        self.tab_widget.addTab(url_parser_tab, "地址解析")

        # 退出按钮
        exit_btn = QPushButton("强制退出")
        exit_btn.clicked.connect(self.close_application)
        main_layout.addWidget(exit_btn)

        # 更新历史记录显示
        self.update_history_display()

    def on_history_double_clicked(self, item):
        """处理历史记录双击事件"""
        history_item = self.history[item.data(Qt.UserRole)]
        # 复制到剪贴板
        QApplication.clipboard().setText(history_item.url)
        # 开始新的下载任务
        self.start_download(history_item.url, history_item)

    def copy_selected_history(self):
        """复制选中的历史记录到剪贴板"""
        selected_items = self.history_list.selectedItems()
        if selected_items:
            urls = []
            for item in selected_items:
                history_item = self.history[item.data(Qt.UserRole)]
                urls.append(history_item.url)
            QApplication.clipboard().setText('\n'.join(urls))

    def update_save_format(self, format):
        """更新保存格式"""
        self.save_format = format

    def update_excluded_formats(self):
        """更新排除的图片格式"""
        for fmt, checkbox in self.format_checkboxes.items():
            self.excluded_formats[fmt] = not checkbox.isChecked()

    def close_application(self):
        """安全关闭应用程序"""
        if self.is_closing:
            return

        self.is_closing = True
        # 停止所有下载线程
        for worker in self.workers:
            if worker.isRunning():
                worker.stop()

        # 等待所有线程完成
        for worker in self.workers:
            worker.wait()

        self.close()

    def closeEvent(self, event):
        """重写关闭事件"""
        self.close_application()
        event.accept()

    def load_history(self):
        """加载历史记录"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return [HistoryItem.from_dict(item) for item in data]
        except Exception as e:
            self.logger.error(f"加载历史记录失败: {str(e)}")
        return []

    def save_history(self):
        """保存历史记录"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump([item.to_dict() for item in self.history], f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存历史记录失败: {str(e)}")

    def update_history_display(self):
        self.history_list.clear()
        for i, item in enumerate(self.history):
            list_item = QListWidgetItem(
                f"{item.url} ({item.timestamp.strftime('%Y-%m-%d %H:%M:%S')}) "
                f"[下载次数: {item.download_count}]"
            )
            list_item.setData(Qt.UserRole, i)
            self.history_list.addItem(list_item)

    def add_url(self):
        """添加URL并开始下载"""
        try:
            # 支持分号分隔的多个URL
            urls = [url.strip() for url in self.url_input.text().split('；') if url.strip()]
            if not urls:
                # 尝试使用英文分号分割
                urls = [url.strip() for url in self.url_input.text().split(';') if url.strip()]

            if not urls:
                self.logger.warning("未输入有效的URL")
                return

            self.logger.info(f"开始处理URL: {urls}")

            for url in urls:
                # 移除URL开头的@符号（如果存在）
                url = url.lstrip('@')

                # 查找或创建历史记录项
                history_item = next((item for item in self.history if item.url == url), None)
                if not history_item:
                    history_item = HistoryItem(url)
                    self.history.append(history_item)

                # 开始下载任务
                self.start_download(url, history_item)

            self.save_history()
            self.update_history_display()
            self.url_input.clear()

        except Exception as e:
            self.logger.error(f"添加URL时发生错误: {str(e)}")

    def start_download(self, url, history_item):
        """开始下载任务"""
        self.logger.info(f"开始下载任务: {url}")

        # 提取URL部分（如果输入包含标题）
        if "：" in url:
            try:
                _, url = url.split("：", 1)
                url = url.strip()
            except:
                pass

        # 更新下载次数（延迟保存以减少IO阻塞）
        if history_item:
            history_item.download_count += 1

        # 创建进度条
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)

        url_label = QLabel(url)
        url_label.setWordWrap(True)
        progress_layout.addWidget(url_label)

        progress_bar = QProgressBar()
        progress_bar.setRange(0, 100)
        progress_layout.addWidget(progress_bar)

        self.progress_bars[url] = progress_bar
        self.progress_layout.addWidget(progress_frame)

        # 创建下载线程
        worker = DownloadWorker(url, self.save_dir, self.excluded_formats, self.save_format)
        worker.progress_updated.connect(self.update_progress)
        worker.download_completed.connect(self.download_completed)
        worker.download_error.connect(self.download_error)

        self.workers.append(worker)
        worker.start()

        # 切换到下载列表标签页
        self.tab_widget.setCurrentIndex(1)

    @Slot(str, int, int)
    def update_progress(self, url, current, total):
        """更新下载进度"""
        if url in self.progress_bars:
            progress = int((current / total) * 100) if total > 0 else 0
            self.progress_bars[url].setValue(progress)
            self.logger.info(f"下载进度 {url}: {progress}%")

    @Slot(str, str)
    def download_completed(self, url, save_path):
        """下载完成处理"""
        self.logger.info(f"下载完成: {url} -> {save_path}")
        if url in self.progress_bars:
            self.progress_bars[url].setValue(100)

        # 保存历史记录（在下载完成时保存，减少频繁IO）
        self.save_history()
        self.update_history_display()

    @Slot(str, str)
    def download_error(self, url, error):
        """下载错误处理"""
        self.logger.error(f"下载错误 {url}: {error}")
        if url in self.progress_bars:
            self.progress_bars[url].setValue(0)

        # 保存历史记录（在下载出错时也保存）
        self.save_history()
        self.update_history_display()

    def change_save_dir(self):
        new_dir = QFileDialog.getExistingDirectory(self, "选择保存目录", self.save_dir)
        if new_dir:
            self.save_dir = new_dir
            self.dir_label.setText(f"保存目录: {self.save_dir}")
            self.logger.info(f"更改保存目录: {self.save_dir}")

    def clear_history(self):
        self.history.clear()
        self.save_history()
        self.update_history_display()
        self.logger.info("清空历史记录")

    def delete_selected_history(self):
        selected_items = self.history_list.selectedItems()
        for item in selected_items:
            index = item.data(Qt.UserRole)
            self.history.pop(index)
        self.save_history()
        self.update_history_display()
        self.logger.info(f"删除选中的历史记录: {len(selected_items)} 条")

    def parse_urls(self):
        """解析网页中的地址"""
        url = self.parser_url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "警告", "请输入有效的网页地址")
            return

        try:
            # 检查是否是格式化好的地址列表
            if "：" in url and "；" in url:
                # 处理格式化好的地址列表
                results = []
                urls_to_download = []  # 存储要下载的URL
                # 按分号分割多个地址
                url_pairs = url.split("；")
                for pair in url_pairs:
                    if not pair.strip():
                        continue
                    # 按冒号分割标题和URL
                    if "：" in pair:
                        title, url = pair.split("：", 1)
                        url = url.strip()
                        # 确保URL是有效的
                        if url.startswith(('http://', 'https://')):
                            results.append(f"{title.strip()}：{url}；")
                            urls_to_download.append(url)

                if results:
                    self.result_text.setPlainText('\n'.join(results))
                    self.save_results()
                    QMessageBox.information(self, "提示", f"成功解析出 {len(results)} 个地址，请点击【批量一键下载】按钮开始下载")
                    return

            # 如果不是格式化好的地址列表，则按原来的方式处理
            # 发送请求获取网页内容
            response = requests.get(url)
            response.raise_for_status()

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找所有链接
            links = soup.find_all('a', class_='module-play-list-link')

            if not links:
                QMessageBox.information(self, "提示", "未找到符合条件的链接")
                return

            # 构建基础URL
            base_url = '/'.join(url.split('/')[:3])  # 获取域名部分

            # 处理结果
            results = []
            urls_to_download = []  # 存储要下载的URL
            for link in links:
                title = link.get('title', '').strip()
                href = link.get('href', '').strip()
                if href:
                    full_url = base_url + href if href.startswith('/') else href
                    results.append(f"{title}：{full_url}；")
                    urls_to_download.append(full_url)

            # 显示结果
            self.result_text.setPlainText('\n'.join(results))

            # 保存到文件
            self.save_results()

            # 提示用户解析完成
            QMessageBox.information(self, "提示", f"成功解析出 {len(results)} 个地址，请点击【批量一键下载】按钮开始下载")

        except Exception as e:
            self.logger.error(f"解析URL时发生错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"解析失败: {str(e)}")

    def copy_all_results(self):
        """复制所有结果到剪贴板"""
        text = self.result_text.toPlainText()
        if text:
            QApplication.clipboard().setText(text)
            QMessageBox.information(self, "提示", "已复制到剪贴板")

    def save_results(self):
        """保存结果到文件"""
        text = self.result_text.toPlainText()
        if not text:
            return

        try:
            # 生成文件名
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"urls_{timestamp}.txt"
            filepath = os.path.join(self.save_dir, filename)

            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(text)

            self.logger.info(f"结果已保存到: {filepath}")
            # QMessageBox.information(self, "提示", f"结果已保存到: {filepath}")

        except Exception as e:
            self.logger.error(f"保存结果时发生错误: {str(e)}")
            # QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")

    def batch_download_from_results(self):
        """从解析结果中批量创建下载任务"""
        text = self.result_text.toPlainText()
        if not text:
            QMessageBox.warning(self, "警告", "没有可下载的地址，请先进行地址解析")
            return

        try:
            # 解析结果文本，提取所有URL
            urls_to_download = []
            lines = text.strip().split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 处理格式化的地址（标题：URL；）
                if "：" in line:
                    parts = line.split("：", 1)
                    if len(parts) == 2:
                        url = parts[1].strip().rstrip('；')
                        if url.startswith(('http://', 'https://')):
                            urls_to_download.append(url)
                # 处理纯URL
                elif line.startswith(('http://', 'https://')):
                    urls_to_download.append(line)

            if not urls_to_download:
                QMessageBox.warning(self, "警告", "未找到有效的下载地址")
                return

            # 检查当前运行的下载任务数量
            running_downloads = sum(1 for worker in self.workers if worker.isRunning())
            if running_downloads >= self.max_concurrent_downloads:
                reply = QMessageBox.question(
                    self,
                    "确认下载",
                    f"当前有 {running_downloads} 个下载任务正在运行。\n"
                    f"找到 {len(urls_to_download)} 个新地址，确定要加入下载队列吗？\n"
                    f"新任务将会逐个启动以避免系统过载。",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
            else:
                reply = QMessageBox.question(
                    self,
                    "确认下载",
                    f"找到 {len(urls_to_download)} 个地址，确定要开始批量下载吗？\n"
                    f"任务将会逐个启动以避免系统过载。",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

            if reply != QMessageBox.Yes:
                return

            # 将URL添加到批量下载队列
            for url in urls_to_download:
                # 查找或创建历史记录项
                history_item = next((item for item in self.history if item.url == url), None)
                if not history_item:
                    history_item = HistoryItem(url)
                    self.history.append(history_item)

                self.batch_download_queue.append((url, history_item))

            # 保存历史记录
            self.save_history()
            self.update_history_display()

            # 显示信息并开始处理队列
            QMessageBox.information(self, "提示", f"已将 {len(urls_to_download)} 个下载任务加入队列，正在逐个启动...")

            # 切换到下载列表标签页
            self.tab_widget.setCurrentIndex(1)

            # 开始处理批量下载队列
            if not self.batch_download_timer.isActive():
                self.process_next_batch_download()

        except Exception as e:
            self.logger.error(f"批量下载时发生错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"批量下载失败: {str(e)}")

    def process_next_batch_download(self):
        """处理批量下载队列中的下一个任务"""
        try:
            # 检查队列是否为空
            if not self.batch_download_queue:
                self.logger.info("批量下载队列已完成")
                return

            # 检查当前运行的下载任务数量
            running_downloads = sum(1 for worker in self.workers if worker.isRunning())

            # 如果当前运行的下载任务数量已达到最大值，等待一段时间后再检查
            if running_downloads >= self.max_concurrent_downloads:
                self.logger.info(f"当前运行 {running_downloads} 个下载任务，等待空闲...")
                self.batch_download_timer.start(self.batch_download_interval * 2)  # 等待更长时间
                return

            # 从队列中取出下一个任务
            url, history_item = self.batch_download_queue.pop(0)

            # 启动下载任务
            self.logger.info(f"从队列启动下载任务: {url} (剩余队列: {len(self.batch_download_queue)})")
            self.start_download(url, history_item)

            # 如果队列中还有任务，设置定时器处理下一个
            if self.batch_download_queue:
                self.batch_download_timer.start(self.batch_download_interval)
            else:
                self.logger.info("所有批量下载任务已启动完成")

        except Exception as e:
            self.logger.error(f"处理批量下载队列时发生错误: {str(e)}")
            # 如果出错，继续处理队列中的下一个任务
            if self.batch_download_queue:
                self.batch_download_timer.start(self.batch_download_interval)

if __name__ == '__main__':
    # 设置应用程序属性以支持安全编码
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)

    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())