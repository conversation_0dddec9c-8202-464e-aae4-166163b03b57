import os
import sys
import shutil
import subprocess
from pathlib import Path

def run_command(command):
    """运行命令并打印输出"""
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        shell=True
    )
    
    for line in process.stdout:
        print(line, end='')
    
    process.wait()
    return process.returncode

def build_app():
    """构建应用程序"""
    print("开始构建应用...")
    
    # 清理之前的构建文件
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 使用PyInstaller打包应用
    pyinstaller_cmd = (
        "pyinstaller "
        "--name='网页图片抓取工具' "
        "--windowed "  # 不显示控制台窗口
        "--icon=icon.icns "  # 应用图标
        "--clean "  # 清理临时文件
        "--noconfirm "  # 不确认覆盖
        "--add-data 'image_downloader.py:.' "  # 添加依赖模块
        "--hidden-import=PIL "  # 添加隐藏导入
        "--hidden-import=PIL._tkinter "
        "--hidden-import=bs4 "
        "--hidden-import=lxml "
        "--hidden-import=requests "
        "--hidden-import=urllib3 "
        "--hidden-import=chardet "
        "--hidden-import=certifi "
        "--hidden-import=idna "
        "--hidden-import=charset_normalizer "
        "main.py"
    )
    
    if run_command(pyinstaller_cmd) != 0:
        print("打包失败！")
        return False
    
    print("应用程序打包完成！")
    return True

def create_dmg():
    """创建DMG安装包"""
    print("开始创建DMG安装包...")
    
    # 创建临时目录
    temp_dir = "temp_dmg"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    
    # 复制应用到临时目录
    app_path = os.path.join("dist", "网页图片抓取工具.app")
    shutil.copytree(app_path, os.path.join(temp_dir, "网页图片抓取工具.app"))
    
    # 创建DMG文件
    dmg_path = os.path.join("dist", "网页图片抓取工具.dmg")
    if os.path.exists(dmg_path):
        os.remove(dmg_path)
    
    create_dmg_cmd = (
        f"hdiutil create -volname '网页图片抓取工具' "
        f"-srcfolder {temp_dir} "
        f"-ov -format UDZO {dmg_path}"
    )
    
    if run_command(create_dmg_cmd) != 0:
        print("创建DMG失败！")
        return False
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    print("DMG安装包创建完成！")
    return True

def main():
    # 检查是否安装了必要的工具
    try:
        import PyInstaller
    except ImportError:
        print("正在安装 PyInstaller...")
        if run_command("pip install pyinstaller") != 0:
            print("安装 PyInstaller 失败！")
            return
    
    # 检查是否安装了所有依赖
    required_packages = [
        "PySide6",
        "Pillow",
        "beautifulsoup4",
        "lxml",
        "requests"
    ]
    
    for package in required_packages:
        try:
            __import__(package.lower())
        except ImportError:
            print(f"正在安装 {package}...")
            if run_command(f"pip install {package}") != 0:
                print(f"安装 {package} 失败！")
                return
    
    # 构建应用程序
    if not build_app():
        return
    
    # 创建DMG安装包
    if not create_dmg():
        return
    
    print("\n打包完成！")
    print(f"安装包位置：{os.path.join('dist', '网页图片抓取工具.dmg')}")

if __name__ == "__main__":
    main() 