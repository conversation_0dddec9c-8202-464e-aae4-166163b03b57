import os
import sys
import shutil
import subprocess
from datetime import datetime

def run_command(command):
    """运行命令并返回状态码"""
    process = subprocess.Popen(command, shell=True)
    process.wait()
    return process.returncode

def build_app():
    """构建应用程序"""
    print("开始构建应用...")
    
    # 清理之前的构建文件
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 使用PyInstaller打包应用
    pyinstaller_cmd = (
        "pyinstaller "
        "--name='网页图片抓取工具' "
        "--windowed "  # 不显示控制台窗口
        "--icon=icon.icns "  # 应用图标
        "--clean "  # 清理临时文件
        "--noconfirm "  # 不确认覆盖
        "--add-data 'image_downloader:image_downloader' "  # 添加依赖模块
        "--hidden-import=PIL "  # 添加隐藏导入
        "--hidden-import=PIL._tkinter "
        "--hidden-import=bs4 "
        "--hidden-import=lxml "
        "--hidden-import=requests "
        "--hidden-import=urllib3 "
        "--hidden-import=chardet "
        "--hidden-import=certifi "
        "--hidden-import=idna "
        "--hidden-import=charset_normalizer "
        "main.py"
    )
    
    if run_command(pyinstaller_cmd) != 0:
        print("打包失败！")
        return False
    
    print("应用程序打包完成！")
    return True

def create_dmg():
    """创建DMG安装包"""
    print("开始创建DMG安装包...")
    
    # 设置变量
    app_name = "网页图片抓取工具"
    dmg_name = f"{app_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    app_path = f"dist/{app_name}.app"
    dmg_path = f"dist/{dmg_name}.dmg"
    
    # 检查应用是否存在
    if not os.path.exists(app_path):
        print("应用文件不存在！")
        return False
    
    # 创建临时目录
    temp_dir = "temp_dmg"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    
    # 复制应用到临时目录
    shutil.copytree(app_path, os.path.join(temp_dir, f"{app_name}.app"))
    
    # 创建DMG
    create_dmg_cmd = (
        f"hdiutil create -volname '{app_name}' "
        f"-srcfolder {temp_dir} "
        f"-ov -format UDZO "
        f"{dmg_path}"
    )
    
    if run_command(create_dmg_cmd) != 0:
        print("创建DMG失败！")
        shutil.rmtree(temp_dir)
        return False
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    print(f"DMG安装包创建完成：{dmg_path}")
    return True

def main():
    """主函数"""
    if not build_app():
        return
    
    if not create_dmg():
        return
    
    print("打包完成！")

if __name__ == "__main__":
    main() 