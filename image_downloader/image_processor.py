import io
from PIL import Image
from .logger import logger

class ImageProcessor:
    @staticmethod
    def convert_format(image_data, target_format):
        """转换图片格式"""
        try:
            # 从二进制数据创建图片对象
            img = Image.open(io.BytesIO(image_data))
            
            # 如果是PNG格式且需要转换为JPG，需要处理透明背景
            if target_format.lower() == "jpg" and img.mode in ("RGBA", "LA"):
                logger.debug("处理PNG透明背景")
                background = Image.new("RGB", img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1])
                img = background
            
            # 转换为RGB模式（如果需要）
            if img.mode not in ("RGB", "L"):
                logger.debug(f"转换图片模式从 {img.mode} 到 RGB")
                img = img.convert("RGB")
            
            # 保存为新的格式
            output = io.BytesIO()
            save_format = target_format.upper()
            if save_format == "JPG":
                save_format = "JPEG"
            img.save(output, format=save_format, quality=95)
            logger.debug(f"图片格式转换成功: {save_format}")
            return output.getvalue()
        except Exception as e:
            logger.error(f"转换图片格式失败: {str(e)}")
            return None

    @staticmethod
    def extract_images_from_soup(soup, base_url, excluded_formats):
        """从BeautifulSoup对象中提取图片URL，保持DOM顺序"""
        images = []
        
        # 查找所有可能包含图片的元素
        for element in soup.find_all(['img', 'div', 'figure']):
            # 如果是img标签
            if element.name == 'img':
                img_url = element.get('src') or element.get('data-src') or element.get('data-original')
                if img_url:
                    from .utils import make_absolute_url
                    img_url = make_absolute_url(base_url, img_url)
                    
                    # 检查图片格式
                    ext = img_url.split('.')[-1].lower()
                    if ext in excluded_formats and excluded_formats[ext]:
                        logger.debug(f"跳过格式为 {ext} 的图片: {img_url}")
                        continue
                    
                    # 获取alt文本作为文件名
                    alt_text = element.get('alt', '')
                    images.append((img_url, alt_text))
                    logger.debug(f"找到图片: {img_url} (alt: {alt_text})")
            
            # 如果是div或figure标签，查找其中的img标签
            else:
                for img in element.find_all('img'):
                    img_url = img.get('src') or img.get('data-src') or img.get('data-original')
                    if img_url:
                        from .utils import make_absolute_url
                        img_url = make_absolute_url(base_url, img_url)
                        
                        # 检查图片格式
                        ext = img_url.split('.')[-1].lower()
                        if ext in excluded_formats and excluded_formats[ext]:
                            logger.debug(f"跳过格式为 {ext} 的图片: {img_url}")
                            continue
                        
                        # 获取alt文本作为文件名
                        alt_text = img.get('alt', '')
                        images.append((img_url, alt_text))
                        logger.debug(f"找到图片: {img_url} (alt: {alt_text})")
        
        logger.info(f"共找到 {len(images)} 张图片")
        return images
