# 网页图片抓取工具

一个简单易用的网页图片批量下载工具，支持多页面解析和批量下载。

## 功能特点

- 支持多个网页地址批量解析
- 支持图片格式过滤
- 支持图片格式转换
- 支持历史记录管理
- 支持批量一键下载
- 支持地址解析和批量下载分离

## 系统要求

- macOS 10.15 或更高版本
- Python 3.8 或更高版本

## 安装依赖

```bash
pip install -r requirements.txt
```

## 构建应用

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行构建脚本：
```bash
python build_macos.py
```

构建完成后，将在 `dist` 目录下生成以下文件：
- `网页图片抓取工具.app`：应用程序
- `网页图片抓取工具_YYYYMMDD_HHMMSS.dmg`：安装包

## 使用说明

1. 双击 DMG 文件打开
2. 将应用拖到 Applications 文件夹
3. 从 Applications 文件夹启动应用

## 注意事项

- 首次运行时，应用会在用户目录下创建数据文件夹
- 下载的图片默认保存在 Downloads 文件夹
- 支持自定义保存目录
- 支持多种图片格式（jpg、png、gif、webp、svg）
- 支持自定义保存格式（webp、jpg、png） 