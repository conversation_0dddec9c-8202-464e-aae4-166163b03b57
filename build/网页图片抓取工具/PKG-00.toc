('/Users/<USER>/WK/gitee/tool_img-spider/build/网页图片抓取工具/网页图片抓取工具.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/WK/gitee/tool_img-spider/build/网页图片抓取工具/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_struct.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/zlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/WK/gitee/tool_img-spider/build/网页图片抓取工具/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/WK/gitee/tool_img-spider/build/网页图片抓取工具/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/WK/gitee/tool_img-spider/build/网页图片抓取工具/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/WK/gitee/tool_img-spider/build/网页图片抓取工具/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('main', '/Users/<USER>/WK/gitee/tool_img-spider/main.py', 'PYSOURCE')],
 'Python',
 True,
 False,
 False,
 [],
 'x86_64',
 None,
 None)
